# Environment Configuration Example
# Copy this file to .env.local for development or .env.prod for production
# and fill in your actual values

# Database Configuration - Neon PostgreSQL
DB_HOST=your-neon-host.neon.tech
DB_PORT=5432
DB_NAME=your_database_name
DB_USER=your_database_user
DB_PASSWORD=your_database_password
DB_SSL=true

# pCloud Configuration
PCLOUD_USERNAME=<EMAIL>
PCLOUD_PASSWORD=your_pcloud_password
PCLOUD_REGION=us
PCLOUD_REMOTE_FOLDER=/CSV_Backups
PCLOUD_BASE_FOLDER=/csv_backup

# Application Configuration
NODE_ENV=development
APP_NAME=Eposservice
APP_VERSION=0.0.0

# CSV Processing Configuration
CSV_FOLDER_PATH=/path/to/your/csv/folder
CSV_BACKUP_PATH=/path/to/your/backup/folder
CSV_AUTO_PROCESSING=true
CSV_PROCESS_DELAY=2000
CSV_ENABLE_BACKUP=true
CSV_ENABLE_CLEANUP=true
CSV_ENABLE_PCLOUD_BACKUP=false

# Connection Timeouts (in milliseconds)
DB_CONNECTION_TIMEOUT=10000
DB_QUERY_TIMEOUT=30000
PCLOUD_TIMEOUT=10000
PCLOUD_UPLOAD_TIMEOUT=60000

# Production specific settings (only for .env.prod)
# LOG_LEVEL=error
# ENABLE_DEBUG=false
