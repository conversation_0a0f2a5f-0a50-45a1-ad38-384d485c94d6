# Local Development Environment Configuration
# This file is for local development only

# Database Configuration - Neon PostgreSQL
DB_HOST=ep-dawn-fog-a1jk7z7f-pooler.ap-southeast-1.aws.neon.tech
DB_PORT=5432
DB_NAME=neondb
DB_USER=neondb_owner
DB_PASSWORD=npg_v1aKnJdNXif4
DB_SSL=true

# pCloud Configuration
PCLOUD_USERNAME=<EMAIL>
PCLOUD_PASSWORD=36890764
PCLOUD_REGION=us
PCLOUD_REMOTE_FOLDER=/CSV_Backups
PCLOUD_BASE_FOLDER=/csv_backup

# Application Configuration
NODE_ENV=development
APP_NAME=Eposservice Development
APP_VERSION=0.0.0-dev

# CSV Processing Configuration - LOCAL DEVELOPMENT
CSV_FOLDER_PATH=/Users/<USER>/Desktop/simple_csv
CSV_BACKUP_PATH=/Users/<USER>/Desktop/simple_csv_bk
CSV_AUTO_PROCESSING=true
CSV_PROCESS_DELAY=3000
CSV_ENABLE_BACKUP=true
CSV_ENABLE_CLEANUP=true
CSV_ENABLE_PCLOUD_BACKUP=false

# Connection Timeouts (in milliseconds)
DB_CONNECTION_TIMEOUT=10000
DB_QUERY_TIMEOUT=30000
PCLOUD_TIMEOUT=10000
PCLOUD_UPLOAD_TIMEOUT=60000
