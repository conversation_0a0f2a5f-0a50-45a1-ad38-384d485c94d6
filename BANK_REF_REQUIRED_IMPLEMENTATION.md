# Bank Reference Required Field Implementation

## Overview
Enhanced the Bank Master screen to make the `bank_ref` field a required field instead of optional. This implementation includes frontend validation, backend validation, and database constraints.

## Changes Made

### 1. Frontend Changes (`src/renderer/screens/bank-master.screen.tsx`)

#### Interface Updates
- Updated `BankMaster` interface to make `bank_ref` required (removed `?` optional marker)
- Changed from `bank_ref?: string` to `bank_ref: string`

#### Form Enhancements
- Added red asterisk (*) to Bank Reference label to indicate required field
- Added `required` attribute to the bank_ref input field
- Updated placeholder text and help text
- Changed layout from 2-column to 3-column grid to accommodate Bank Reference field

#### Validation
- Added frontend validation to check if `bank_ref` is provided and not empty
- Added length validation (maximum 20 characters)
- Shows appropriate error messages for missing or invalid bank_ref

#### Table Display
- Added "Bank Reference" column to the banks table
- Added sorting capability for bank_ref column
- Updated search functionality to include bank_ref in search queries
- Updated search placeholder text to mention "reference"

### 2. Backend Changes (`src/main/handler/bankMasterHandler.ts`)

#### Interface Updates
- Updated `BankMaster` interface to make `bank_ref` required

#### Database Operations
- **SELECT**: Added `bank_ref` to all SELECT queries
- **INSERT**: Added `bank_ref` to INSERT query with proper validation
- **UPDATE**: Added `bank_ref` to UPDATE query with proper validation
- **SEARCH**: Added `bank_ref` to search functionality

#### Validation
- Added server-side validation for both CREATE and UPDATE operations
- Validates that `bank_ref` is provided and not empty
- Added `bank_ref` to allowed sort columns

### 3. Database Changes

#### Migration Script (`database_migration_make_bank_ref_required.sql`)
- Updates existing NULL `bank_ref` values with default format: `REF_{bank_code}`
- Makes `bank_ref` column NOT NULL
- Adds check constraint to ensure `bank_ref` is not empty
- Adds documentation comment

#### Database Constraints
- `bank_ref` column is now NOT NULL
- Check constraint: `chk_bank_ref_not_empty` ensures the field is not empty
- Maximum length: 20 characters (VARCHAR(20))

## Features Implemented

### ✅ Required Field Validation
- Frontend validation prevents form submission without bank_ref
- Backend validation returns error if bank_ref is missing
- Database constraint prevents NULL values

### ✅ Create Operation
- New banks must have a bank_ref value entered
- Field is validated on both frontend and backend
- Proper error handling and user feedback

### ✅ Update Operation
- Existing banks must maintain a valid bank_ref
- Field preserves existing values when editing
- Cannot be cleared or set to empty

### ✅ Search Functionality
- Bank reference is included in search queries
- Users can search by bank reference
- Case-insensitive search support

### ✅ Table Display
- "Bank Reference" column in the banks table
- Shows bank_ref value (no longer shows "-" for empty)
- Sortable column with proper ordering

### ✅ Data Migration
- Existing records with NULL bank_ref were updated with default values
- Format: `REF_{bank_code}` (e.g., "REF_BBL", "REF_SCB")
- No data loss during migration

## Database Schema

```sql
-- Updated column definition
ALTER TABLE tmst_bank 
ALTER COLUMN bank_ref SET NOT NULL;

-- Check constraint
ALTER TABLE tmst_bank 
ADD CONSTRAINT chk_bank_ref_not_empty 
CHECK (bank_ref IS NOT NULL AND LENGTH(TRIM(bank_ref)) > 0);
```

## Testing

The implementation was thoroughly tested with:
1. **SELECT queries** - Verified bank_ref is included in results
2. **INSERT operations** - Confirmed required validation works
3. **UPDATE operations** - Verified bank_ref can be updated but not removed
4. **Search functionality** - Tested search by bank_ref
5. **Database constraints** - Verified NULL values are rejected
6. **Migration** - Confirmed existing data was properly updated

## Usage

### Creating a New Bank
1. Bank Code: Required (max 20 chars)
2. **Bank Reference: Required (max 20 chars)** ⭐ NEW REQUIREMENT
3. Status: Required (Active/Inactive)
4. Bank Names: At least one required (Thai or English)
5. Addresses: Optional

### Validation Messages
- "Bank reference is required" - when field is empty
- "Bank reference must be 20 characters or less" - when too long

## Migration Instructions

1. **Database Migration**: Already completed
   - Existing NULL values updated to `REF_{bank_code}` format
   - Column set to NOT NULL with check constraint

2. **Application Update**: Deploy updated code
   - Frontend will now require bank_ref input
   - Backend will validate bank_ref on all operations

## Notes

- All existing banks now have valid bank_ref values
- The field is consistently required across all operations
- Search functionality includes bank_ref for better discoverability
- Database constraints ensure data integrity
