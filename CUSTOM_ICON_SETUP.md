# Custom Icon Setup for Electron Application

This document explains how the Electron application has been configured to use the custom Epos.png logo for all system alerts, notifications, and popups.

## Overview

The application now uses the custom logo (`src/assets/Epos.png`) for:
- System notification popups (Electron Notification API)
- Dialog boxes and alert windows
- Application icon in the system tray
- Main application window icon
- All popup windows and alerts

## Files Modified/Created

### Main Process Files

1. **`src/main/index.ts`**
   - Added custom icon loading functionality
   - Set main window icon
   - Added system tray cleanup on app quit

2. **`src/main/handler/notificationHandler.ts`** (NEW)
   - Handles system notifications with custom icon
   - Provides various dialog types (error, warning, info, confirmation)
   - All dialogs use the custom icon

3. **`src/main/handler/systemTrayHandler.ts`** (NEW)
   - Creates system tray with custom icon
   - Provides tray context menu and balloon notifications
   - Handles tray click events

4. **`src/main/ipcHandlers.ts`**
   - Added notification and system tray handlers

5. **`src/main/handler/transactionHandler.ts`**
   - Updated save dialog to use custom icon

### Renderer Process Files

6. **`src/renderer/utils/systemNotifications.ts`** (NEW)
   - Utility functions for easy access to custom notifications and dialogs
   - Convenience functions for common use cases

7. **`src/renderer/components/SystemNotificationDemo.tsx`** (NEW)
   - Demo component showing all notification and dialog features

### Configuration Files

8. **`electron-builder.yml`**
   - Added extraResources to include Epos.png in build
   - Ensures icon is available in production builds

## Usage Examples

### System Notifications

```typescript
import { showSystemNotification, showSuccessNotification } from '../utils/systemNotifications';

// Custom notification
await showSystemNotification({
  title: 'Custom Title',
  body: 'Custom message',
  urgency: 'normal'
});

// Convenience functions
showSuccessNotification('Operation completed!');
showErrorNotification('Something went wrong!');
showWarningNotification('Please be careful');
showInfoNotification('Here is some info');
```

### System Dialogs

```typescript
import { 
  showConfirmationDialog, 
  showErrorDialog, 
  showDeleteConfirmation 
} from '../utils/systemNotifications';

// Confirmation dialog
const confirmed = await showConfirmationDialog(
  'Do you want to proceed?',
  'Confirm Action'
);

// Error dialog
await showErrorDialog(
  'An error occurred',
  'Error',
  'Please try again'
);

// Convenience functions
const deleteConfirmed = await showDeleteConfirmation('User Account');
const saveConfirmed = await showSaveConfirmation();
```

### System Tray

```typescript
import { 
  createSystemTray, 
  destroySystemTray, 
  updateTrayTooltip 
} from '../utils/systemNotifications';

// Create system tray
await createSystemTray();

// Update tooltip
await updateTrayTooltip('New status message');

// Show balloon (Windows only)
await showTrayBalloon('Title', 'Message content');

// Destroy tray
await destroySystemTray();
```

## Icon Path Resolution

The application automatically resolves the icon path based on the environment:

- **Development**: `src/assets/Epos.png`
- **Production**: `resources/assets/Epos.png` (packaged with the app)

## Platform Support

### Windows
- ✅ System notifications with custom icon
- ✅ Dialog boxes with custom icon
- ✅ System tray with custom icon
- ✅ Tray balloon notifications

### macOS
- ✅ System notifications with custom icon
- ✅ Dialog boxes with custom icon
- ✅ System tray with custom icon
- ❌ Tray balloon notifications (not supported)

### Linux
- ✅ System notifications with custom icon
- ✅ Dialog boxes with custom icon
- ✅ System tray with custom icon
- ❌ Tray balloon notifications (not supported)

## Available IPC Channels

The following IPC channels are available for the renderer process:

### Notifications
- `show-system-notification`
- `show-custom-dialog`
- `show-confirmation-dialog`
- `show-error-dialog`
- `show-warning-dialog`
- `show-info-dialog`

### System Tray
- `create-system-tray`
- `destroy-system-tray`
- `update-tray-tooltip`
- `show-tray-balloon`

## Error Handling

All functions include proper error handling and will:
- Log errors to the console
- Return success/failure status
- Gracefully fallback if custom icon is not available

## Testing the Implementation

1. **Run the application in development mode:**
   ```bash
   npm run dev
   ```

2. **Test system notifications:**
   - Use the demo component or call the utility functions
   - Check that notifications show the Epos logo

3. **Test dialogs:**
   - Trigger various dialog types
   - Verify the custom icon appears in dialog boxes

4. **Test system tray:**
   - Create the system tray
   - Check the system tray area for the Epos icon
   - Right-click to see the context menu

5. **Build and test production:**
   ```bash
   npm run build
   npm run dist
   ```

## Troubleshooting

### Icon Not Showing
- Verify `src/assets/Epos.png` exists
- Check console for icon loading errors
- Ensure the file is not corrupted

### System Tray Not Working
- Some Linux distributions require additional packages
- Check if the desktop environment supports system tray
- Verify the icon file is accessible

### Notifications Not Appearing
- Check OS notification settings
- Verify notification permissions
- Some systems may block notifications from unknown applications

## Future Enhancements

Possible improvements:
- Support for different icon sizes for different contexts
- Animated tray icons for status indication
- Rich notifications with action buttons
- Custom notification sounds
- Badge counts on the tray icon

## Dependencies

The implementation uses standard Electron APIs:
- `electron.Notification`
- `electron.dialog`
- `electron.Tray`
- `electron.nativeImage`
- `electron.BrowserWindow`

No additional dependencies are required.
