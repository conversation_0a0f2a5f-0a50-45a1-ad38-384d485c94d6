# Drop transaction_summary_report Table Migration

## 🎯 Objective
Drop the `transaction_summary_report` table and keep only `transaction_summary_report_detail` table for simplified data structure.

## 📁 Files Created

### 1. `database_migration_drop_transaction_summary_report.sql`
Comprehensive migration script that:
- ✅ Handles all dependencies properly
- ✅ Removes foreign key constraints
- ✅ Drops dependent views and functions
- ✅ Removes the `report_id` column from detail table
- ✅ Adds necessary columns to detail table
- ✅ Creates new indexes and views
- ✅ Includes verification queries

### 2. `run_drop_migration.sh`
Safe execution script that:
- ✅ Creates backup before migration
- ✅ Asks for user confirmation
- ✅ Runs the migration
- ✅ Provides status updates

### 3. `MIGRATION_IMPACT_ANALYSIS.md`
Detailed analysis of code changes needed

## 🔄 What the Migration Does

### Database Changes:
1. **Drops Dependencies First:**
   - `v_transaction_summary_workflow` view
   - `get_merchant_summary_stats()` function
   - Foreign key constraints
   - Triggers on main table

2. **Drops Main Table:**
   - `transaction_summary_report` table (with CASCADE)

3. **Updates Detail Table:**
   - Removes `report_id` column
   - Adds `batch_id`, `report_date`, `report_time`, `running_number`
   - Adds `processed_files`, `status`, `total_files`, `total_transactions`

4. **Creates New Objects:**
   - New indexes for batch_id, report_date, status
   - New view: `v_transaction_summary_detail_workflow`
   - Updated trigger for detail table

## 🚨 Critical Warnings

### Data Loss
- ⚠️ **ALL data in `transaction_summary_report` will be permanently lost**
- ⚠️ **Existing `report_id` references will be removed**
- ⚠️ **This action cannot be undone**

### Application Impact
- ❌ **IPC handlers will break** (`get-transaction-summary-reports`, etc.)
- ❌ **Transaction summary service will break**
- ❌ **Frontend components may break**

## 🛠 How to Run Migration

### Option 1: Safe Script (Recommended)
```bash
./run_drop_migration.sh
```

### Option 2: Direct SQL
```bash
psql "your_connection_string" -f database_migration_drop_transaction_summary_report.sql
```

## ✅ Post-Migration Steps

### 1. Update Application Code
You **MUST** update these files:

#### `src/main/handler/transactionHandler.ts`
- Update `get-transaction-summary-reports` to query detail table with GROUP BY
- Update `get-transaction-summary-report-details` to query only detail table
- Update `approve-transaction-summary-report` or remove it

#### `src/main/services/transactionSummaryReportService.ts`
- Update `saveReportToDatabase()` to insert only into detail table
- Update `generateSummaryFromUploadedTransactions()` to include batch metadata

### 2. Test Everything
- Upload transaction files
- Verify data saves correctly
- Test transaction summary reports
- Test transfer status functionality

## 📊 New Data Structure

### Before (2 tables):
```
transaction_summary_report (main)
├── id (PK)
├── batch_id
├── report_date
└── ... (metadata)

transaction_summary_report_detail
├── id (PK)
├── report_id (FK) ← REMOVED
├── merchant_vat
└── ... (merchant data)
```

### After (1 table):
```
transaction_summary_report_detail (only table)
├── id (PK)
├── batch_id ← ADDED
├── report_date ← ADDED
├── report_time ← ADDED
├── running_number ← ADDED
├── processed_files ← ADDED
├── status ← ADDED
├── total_files ← ADDED
├── total_transactions ← ADDED
├── merchant_vat
└── ... (existing merchant data)
```

## 🔍 Verification

After migration, check:
```sql
-- Verify main table is gone
SELECT * FROM information_schema.tables WHERE table_name = 'transaction_summary_report';

-- Check new columns in detail table
SELECT column_name FROM information_schema.columns 
WHERE table_name = 'transaction_summary_report_detail' 
AND column_name IN ('batch_id', 'report_date', 'status');

-- Test new view
SELECT * FROM v_transaction_summary_detail_workflow LIMIT 5;
```

## 🆘 Rollback Plan

**There is no automatic rollback!** If you need to restore:
1. Use the backup CSV file created by the script
2. Recreate the original table structure
3. Import the backup data
4. Restore foreign key relationships

## 📞 Support

If issues occur:
1. Check the backup file was created
2. Review migration logs for errors
3. Test application functionality thoroughly
4. Update application code as outlined above
