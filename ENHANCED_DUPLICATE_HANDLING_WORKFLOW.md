# Enhanced Duplicate Transaction Handling Workflow

## Overview

The enhanced duplicate handling workflow provides comprehensive management of duplicate transactions with multiple strategies, detailed logging, and audit capabilities. This system replaces the simple "skip duplicates" approach with a configurable, intelligent duplicate management system.

## Key Features

### 🔧 **Multiple Handling Strategies**
- **SKIP**: Skip duplicates and log them (default behavior)
- **UPDATE**: Update existing transactions with new data
- **LOG_ONLY**: Log duplicates without processing them
- **MERGE**: Merge duplicate data with existing records

### 📊 **Comprehensive Logging**
- Detailed duplicate detection logs
- Amount difference tracking
- File source tracking (same file vs cross-file duplicates)
- Action taken history
- Timestamp tracking

### 📈 **Enhanced Console Output**
Instead of simple: `📊 Transactions: 150 saved, 25 duplicates`

Now provides detailed analysis:
```
📊 Transaction Summary:
   💾 Saved: 150 transactions
   ⚠️ Duplicates: 25 transactions
   🔄 Updated: 5 transactions

📋 Duplicate Analysis:
   📄 Same File Duplicates: 15
   📁 Cross File Duplicates: 10
   💰 Exact Amount Matches: 20
   💸 Amount Differences: 5
   🔍 Sample Duplicate IDs: 4200002645202507026000149512, 4200002645202507026000149513...
```

## Database Schema

### transaction_duplicate_log
```sql
CREATE TABLE transaction_duplicate_log (
    id BIGSERIAL PRIMARY KEY,
    transaction_id VARCHAR(100) NOT NULL,
    original_file TEXT NOT NULL,
    duplicate_file TEXT NOT NULL,
    original_amount DECIMAL(18,2) NOT NULL,
    duplicate_amount DECIMAL(18,2) NOT NULL,
    action_taken VARCHAR(50) NOT NULL,
    detected_at TIMESTAMP NOT NULL,
    
    -- Computed fields
    amount_difference DECIMAL(18,2) GENERATED ALWAYS AS (duplicate_amount - original_amount) STORED,
    files_match BOOLEAN GENERATED ALWAYS AS (original_file = duplicate_file) STORED,
    
    -- Audit fields
    create_by VARCHAR(100) DEFAULT 'SYSTEM',
    create_dt TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### transaction_duplicate_config
```sql
CREATE TABLE transaction_duplicate_config (
    id SERIAL PRIMARY KEY,
    config_name VARCHAR(100) UNIQUE NOT NULL,
    strategy VARCHAR(50) NOT NULL CHECK (strategy IN ('SKIP', 'UPDATE', 'LOG_ONLY', 'MERGE')),
    log_duplicates BOOLEAN DEFAULT true,
    update_fields TEXT[],
    description TEXT,
    is_active BOOLEAN DEFAULT true
);
```

## Configuration Options

### Default Configurations

| Config Name | Strategy | Description |
|-------------|----------|-------------|
| `DEFAULT_SKIP` | SKIP | Skip duplicates and log them (default) |
| `UPDATE_AMOUNT_TIME` | UPDATE | Update amount, time and status for duplicates |
| `LOG_ONLY` | LOG_ONLY | Only log duplicates without processing |
| `MERGE_DATA` | MERGE | Merge duplicate data with existing records |

### Custom Configuration
```typescript
const duplicateHandlingOptions = {
  strategy: 'UPDATE' as const,
  logDuplicates: true,
  updateFields: ['transaction_amount', 'transaction_time', 'transaction_trade_status']
};
```

## Workflow Process

### 1. Duplicate Detection
When processing transactions, the system:
1. Checks if `transaction_id` already exists
2. Retrieves detailed information about existing transaction
3. Creates a duplicate log entry with comparison data

### 2. Strategy Execution
Based on the configured strategy:

#### SKIP Strategy
- Increments duplicate counter
- Logs the duplicate event
- Continues to next transaction
- **Console Output**: `⚠️ Duplicate transaction skipped: 4200002645202507026000149512`

#### UPDATE Strategy
- Updates specified fields in existing transaction
- Logs the update action
- Increments updated counter
- **Console Output**: `🔄 Duplicate transaction updated: 4200002645202507026000149512`

#### LOG_ONLY Strategy
- Only logs the duplicate without any processing
- Useful for analysis and reporting
- **Console Output**: `📝 Duplicate transaction logged only: 4200002645202507026000149512`

#### MERGE Strategy
- Combines data from both transactions
- Preserves non-null values from new transaction
- Concatenates file names
- **Console Output**: `🔀 Duplicate transaction merged: 4200002645202507026000149512`

### 3. Detailed Logging
For each duplicate, the system logs:
```
⚠️ Duplicate Details for file.csv:
  1. Transaction ID: 4200002645202507026000149512
     Original Amount: ฿13,025.00
     Duplicate Amount: ฿13,025.00
     Amount Difference: ฿0.00
     Original File: wechat_transactions_2025-07-18.csv
     Duplicate File: wechat_transactions_2025-07-19.csv
     Action Taken: SKIP
     Files Match: No
```

## Implementation Details

### Enhanced TransactionProcessingService
```typescript
interface DuplicateHandlingOptions {
  strategy: 'SKIP' | 'UPDATE' | 'LOG_ONLY' | 'MERGE';
  logDuplicates: boolean;
  updateFields?: string[];
}

async saveTransactions(
  transactions: TransactionRecord[], 
  options?: DuplicateHandlingOptions
): Promise<{
  success: boolean;
  savedCount: number;
  duplicateCount: number;
  updatedCount: number;
  errorCount: number;
  errors: string[];
  duplicateDetails: DuplicateLogEntry[];
}>
```

### Enhanced Transaction Handler
The transaction handler now provides:
- Detailed per-file statistics
- Cross-file duplicate analysis
- Amount difference tracking
- Sample duplicate ID listing

## Benefits

### 1. **Improved Visibility**
- Clear understanding of duplicate patterns
- Detailed logging for audit purposes
- Real-time duplicate analysis

### 2. **Flexible Handling**
- Multiple strategies for different scenarios
- Configurable field updates
- Merge capabilities for data consolidation

### 3. **Better Debugging**
- Detailed console output shows exactly what happened
- Amount differences help identify data issues
- File source tracking helps identify process problems

### 4. **Audit Compliance**
- Complete audit trail of all duplicate handling actions
- Timestamp tracking for compliance
- Detailed comparison data for verification

## Usage Examples

### Basic Usage (Default SKIP)
```typescript
const saveResult = await processingService.saveTransactions(transactions);
// Uses default SKIP strategy with logging
```

### Custom UPDATE Strategy
```typescript
const options = {
  strategy: 'UPDATE' as const,
  logDuplicates: true,
  updateFields: ['transaction_amount', 'transaction_time']
};

const saveResult = await processingService.saveTransactions(transactions, options);
```

### Analysis and Reporting
```sql
-- Get duplicate statistics
SELECT 
  action_taken,
  COUNT(*) as duplicate_count,
  AVG(ABS(amount_difference)) as avg_amount_diff,
  COUNT(CASE WHEN files_match THEN 1 END) as same_file_duplicates
FROM transaction_duplicate_log
WHERE detected_at >= CURRENT_DATE - INTERVAL '7 days'
GROUP BY action_taken;

-- Find transactions with amount differences
SELECT *
FROM transaction_duplicate_log
WHERE ABS(amount_difference) > 0.01
ORDER BY ABS(amount_difference) DESC;
```

## Migration and Deployment

The enhanced duplicate handling system is backward compatible:
- Existing code continues to work with default SKIP behavior
- New features are opt-in through configuration
- Database tables are created automatically via migration

## Troubleshooting

### Common Issues

1. **"Duplicate transaction skipped" messages**
   - **Solution**: This is normal behavior - check duplicate log for details
   - **Analysis**: Review `transaction_duplicate_log` table for patterns

2. **Amount differences in duplicates**
   - **Solution**: Use UPDATE or MERGE strategy to handle differences
   - **Analysis**: Check source files for data consistency

3. **Cross-file duplicates**
   - **Solution**: Review file processing workflow
   - **Analysis**: May indicate files being processed multiple times

The enhanced duplicate handling workflow provides complete control and visibility over duplicate transaction management, ensuring data integrity while providing detailed audit trails and flexible handling options.
