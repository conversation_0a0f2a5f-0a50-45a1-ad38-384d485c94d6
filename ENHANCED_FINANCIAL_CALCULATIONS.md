# Enhanced Financial Calculation Methodology

## Overview

This document outlines the enhanced financial calculation methodology implemented in the Transaction Management System. The calculations ensure precision and accuracy for all monetary computations while maintaining clear business logic.

## Financial Calculation Formula

### Core Calculations

#### 1. MDR Amount
```
MDR Amount = Transaction Amount × (MDR% ÷ 100)
```
- **Source**: `merchant_wechat.wechat_rate`
- **Purpose**: Calculate the Merchant Discount Rate fee
- **Example**: 13,025.00 × (0.9% ÷ 100) = 117.23

#### 2. Transfer Fee
```
Transfer Fee = merchant.transfer_fee
```
- **Source**: `merchant.transfer_fee`
- **Purpose**: Direct fee amount used in Final Net Amount calculation
- **Example**: 50.00 (direct value from database)

#### 3. VAT Amount
```
VAT Amount = (merchant.transfer_fee × VAT% ÷ 100) + (MDR Amount × (VAT% ÷ 100))
```
- **Source**: `network_service.vat_percentage`
- **Purpose**: Calculate VAT on both transfer fee and MDR amount
- **Example**: (50.00 × 7% ÷ 100) + (117.23 × 7% ÷ 100) = 3.50 + 8.21 = 11.71

#### 4. Net Amount (Enhanced)
```
Net Amount = Transaction Amount - (MDR Amount + Transfer Fee + VAT Amount)
```
- **Purpose**: Calculate the net amount after deducting MDR, Transfer Fee, and VAT
- **Example**: 13,025.00 - (117.23 + 50.00 + 11.71) = 12,846.06

#### 5. Withholding Tax (Enhanced)
```
Withholding Tax = (MDR Amount × (Withholding Tax% ÷ 100)) + (Transfer Fee × (Withholding Tax% ÷ 100))
```
- **Source**: `merchant.withholding_tax`
- **Purpose**: Calculate withholding tax on both MDR amount and Transfer Fee
- **Example**: (117.23 × (3% ÷ 100)) + (50.00 × (3% ÷ 100)) = 3.52 + 1.50 = 5.02

#### 6. Reimbursement Fee
```
Reimbursement Fee = Transaction Amount × 0.5%
```
- **Purpose**: Fixed 0.5% reimbursement fee on transaction amount
- **Example**: 13,025.00 × 0.5% = 65.13

#### 7. Final Net Amount
```
Final Net Amount = Total Amount - (Transfer Fee + Reimbursement Fee + Service Fee + CUP Business Tax)
```
- **Purpose**: Calculate the final net amount after all fees
- **Example**: 13,025.00 - (50.00 + 65.13 + 13.03 + 6.51) = 12,890.33

## Additional Fees (Channel-Dependent)

### Service Fee
- **WeChat**: 0% (no service fee)
- **Other channels**: 0.1% of transaction amount

### CUP Business Tax Fee
- **WeChat**: 0% (no CUP business tax)
- **Other channels**: 0.05% of transaction amount

## Precision Handling

All calculations use the `roundToTwoDecimals()` function to ensure:
- Consistent 2-decimal place precision
- Elimination of floating-point arithmetic errors
- Accurate financial reporting

```typescript
private static roundToTwoDecimals(num: number): number {
  return Math.round(num * 100) / 100;
}
```

## Database Schema References

### Key Tables and Fields

| Table | Field | Purpose |
|-------|-------|---------|
| `merchant_wechat` | `wechat_rate` | MDR percentage rate |
| `merchant` | `transfer_fee` | Direct transfer fee amount |
| `merchant` | `withholding_tax` | Withholding tax percentage |
| `merchant` | `settlement_fee` | Settlement processing fee |
| `network_service` | `vat_percentage` | VAT percentage rate |

## Implementation Details

### ReportService Enhancement
- Enhanced calculation logic in `src/renderer/services/reportService.ts`
- Added comprehensive documentation and comments
- Implemented precision rounding for all monetary calculations

### UI Enhancements
- Updated form labels with currency indicators (THB)
- Added helpful placeholder values and descriptions
- Enhanced TypeScript interfaces with documentation

### PDF Report Updates
- Updated Financial Calculation Methodology section
- Clear documentation of all calculation formulas
- Proper formatting and presentation

## Example Calculation

Given:
- Transaction Amount: 13,025.00 THB
- MDR Rate: 0.9%
- Transfer Fee: 50.00 THB
- VAT Rate: 7%
- Withholding Tax Rate: 3%

Results:
- MDR Amount: 117.23 THB
- Transfer Fee: 50.00 THB
- VAT Amount: 11.71 THB (3.50 + 8.21)
- Net Amount: 12,846.06 THB (Enhanced: includes Transfer Fee deduction)
- Withholding Tax: 5.02 THB (Enhanced: 3.52 + 1.50)
- Reimbursement Fee: 65.13 THB
- Final Net Amount: 12,890.33 THB

## Benefits

1. **Accuracy**: Eliminates floating-point precision errors
2. **Clarity**: Clear separation of different fee types
3. **Compliance**: Proper VAT calculation on both transfer fee and MDR
4. **Transparency**: Detailed documentation and calculation breakdown
5. **Maintainability**: Well-documented code with clear business logic

## Migration Notes

The enhanced methodology changes how Transfer Fee and VAT Amount are calculated:

### Previous Method
- Transfer Fee Amount = transfer_fee × VAT%
- VAT Amount = Transfer Fee Amount + (MDR Amount × VAT%)

### Enhanced Method
- Transfer Fee = direct transfer_fee value
- VAT Amount = (transfer_fee × VAT%) + (MDR Amount × VAT%)

This ensures that the transfer fee is used as a direct amount in the Final Net Amount calculation while VAT is still properly calculated on both the transfer fee and MDR amount.
