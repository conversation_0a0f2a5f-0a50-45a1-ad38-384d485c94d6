# Environment Configuration Guide

## ✅ **Fixed Issues**

The environment configuration system has been fixed and is now working correctly. The main issues were:

1. **NODE_ENV was being incorrectly restored** - Fixed to properly set NODE_ENV based on the loaded environment file
2. **Environment files had identical values** - Updated with distinct values for easier verification
3. **Icon path resolution for dev:prod mode** - Fixed to work in both development and production modes

## 🔧 **How It Works Now**

### Environment File Loading Logic

The system now properly loads environment files based on the `APP_ENV` variable:

- `APP_ENV=local` → loads `.env.local`
- `APP_ENV=prod` → loads `.env.prod`

### NODE_ENV Handling

- When loading `.env.local` → sets `NODE_ENV=development`
- When loading `.env.prod` → sets `NODE_ENV=production`
- This ensures the app behaves correctly for each environment

## 📁 **Environment Files**

### `.env.local` (Development)
```bash
# Application Configuration
APP_NAME=Eposservice Development
APP_VERSION=0.0.0-dev
NODE_ENV=development

# CSV Configuration
CSV_FOLDER_PATH=/Users/<USER>/Desktop/simple_csv
CSV_BACKUP_PATH=/Users/<USER>/Desktop/simple_csv_bk
CSV_PROCESS_DELAY=3000
CSV_ENABLE_PCLOUD_BACKUP=false
```

### `.env.prod` (Production)
```bash
# Application Configuration
APP_NAME=Eposservice Production
APP_VERSION=1.0.0
NODE_ENV=production

# CSV Configuration
CSV_FOLDER_PATH=/Users/<USER>/Desktop/simple_csv_prod
CSV_BACKUP_PATH=/Users/<USER>/Desktop/simple_csv_bk_prod
CSV_PROCESS_DELAY=1000
CSV_ENABLE_PCLOUD_BACKUP=true
```

## 🚀 **Scripts and Usage**

### Development Mode
```bash
npm run dev
# Uses: NODE_ENV=development APP_ENV=local
# Loads: .env.local
# Result: Development configuration
```

**Console Output:**
```
🔧 Loading environment from: .env.local (APP_ENV: local, NODE_ENV: development)
✅ Successfully loaded environment from: .env.local
🔄 Set NODE_ENV to: development (from .env.local)
🎯 Final environment: NODE_ENV=development, APP_ENV=local
📱 App: Eposservice Development v0.0.0-dev (development)
📁 CSV: /Users/<USER>/Desktop/simple_csv -> /Users/<USER>/Desktop/simple_csv_bk
☁️ pCloud Backup: false
```

### Production Development Mode
```bash
npm run dev:prod
# Uses: NODE_ENV=production APP_ENV=prod
# Loads: .env.prod
# Result: Production configuration in development
```

**Console Output:**
```
🔧 Loading environment from: .env.prod (APP_ENV: prod, NODE_ENV: development)
✅ Successfully loaded environment from: .env.prod
🔄 Set NODE_ENV to: production (from .env.prod)
🎯 Final environment: NODE_ENV=production, APP_ENV=prod
📱 App: Eposservice Production v1.0.0 (production)
📁 CSV: /Users/<USER>/Desktop/simple_csv_prod -> /Users/<USER>/Desktop/simple_csv_bk_prod
☁️ pCloud Backup: true
```

## 🔍 **Key Differences Between Environments**

| Setting | Local (.env.local) | Production (.env.prod) |
|---------|-------------------|----------------------|
| **App Name** | Eposservice Development | Eposservice Production |
| **Version** | 0.0.0-dev | 1.0.0 |
| **NODE_ENV** | development | production |
| **CSV Folder** | simple_csv | simple_csv_prod |
| **Backup Folder** | simple_csv_bk | simple_csv_bk_prod |
| **Process Delay** | 3000ms | 1000ms |
| **pCloud Backup** | false | true |

## 🛠️ **Technical Implementation**

### Environment Loading (`src/main/config/env.ts`)

```typescript
const loadEnvironment = () => {
  let envFile: string;
  if (appEnv === 'prod') {
    envFile = '.env.prod';
  } else if (appEnv === 'local') {
    envFile = '.env.local';
  }
  
  // Load the environment file
  const result = dotenv.config({ path: envPath });
  
  // Set NODE_ENV based on the loaded file
  if (envFile === '.env.prod') {
    process.env.NODE_ENV = 'production';
  } else if (envFile === '.env.local') {
    process.env.NODE_ENV = 'development';
  }
};
```

### Icon Path Resolution

The icon path now works correctly in both modes:

```typescript
const getCustomIconPath = () => {
  const devIconPath = path.join(__dirname, '../../src/assets/Epos.png');
  const prodIconPath = path.join(process.resourcesPath, 'assets/Epos.png');
  
  // Try development path first (works for both dev and dev:prod)
  if (fs.existsSync(devIconPath)) {
    return devIconPath;
  }
  
  // Fall back to production path
  return prodIconPath;
};
```

## ✅ **Verification Steps**

### 1. Test Local Development
```bash
npm run dev
```
**Expected Output:**
- App Name: "Eposservice Development"
- Version: "0.0.0-dev"
- NODE_ENV: "development"
- pCloud Backup: false

### 2. Test Production Development
```bash
npm run dev:prod
```
**Expected Output:**
- App Name: "Eposservice Production"
- Version: "1.0.0"
- NODE_ENV: "production"
- pCloud Backup: true

### 3. Check Console Logs
Look for these key indicators:
```
🔧 Loading environment from: .env.local (APP_ENV: local, NODE_ENV: development)
🎯 Final environment: NODE_ENV=development, APP_ENV=local
📱 App: Eposservice Development v0.0.0-dev (development)
```

## 🎯 **Benefits of This Setup**

1. **Clear Environment Separation**: Different configurations for different environments
2. **Proper NODE_ENV Handling**: Ensures production optimizations when needed
3. **Easy Testing**: Can test production configuration without building
4. **Flexible Configuration**: Easy to add new environment-specific settings
5. **Debug-Friendly**: Clear console output shows which environment is loaded

## 🔄 **Adding New Environment Variables**

To add new environment-specific variables:

1. **Add to both `.env.local` and `.env.prod`:**
   ```bash
   # .env.local
   NEW_SETTING=development_value
   
   # .env.prod
   NEW_SETTING=production_value
   ```

2. **Update the configuration interface:**
   ```typescript
   // In src/main/config/env.ts
   export interface AppConfig {
     // ... existing properties
     newSetting?: string;
   }
   ```

3. **Add getter method:**
   ```typescript
   public getAppConfig(): AppConfig {
     return {
       // ... existing properties
       newSetting: process.env.NEW_SETTING,
     };
   }
   ```

## 🐛 **Troubleshooting**

### Environment Not Loading
- Check if the `.env.local` or `.env.prod` files exist
- Verify the `APP_ENV` variable is set correctly in package.json scripts
- Look for console messages starting with `🔧 Loading environment from:`

### Wrong Environment Loaded
- Check the console output for `🎯 Final environment:`
- Verify the script you're running (`npm run dev` vs `npm run dev:prod`)
- Ensure the environment files have different values for easy identification

### Icon Not Loading in Production Mode
- The icon path resolution now works for both development modes
- Check console for `✅ Custom icon loaded successfully` or `⚠️ Custom icon not found`

## 🎉 **Success!**

Your environment configuration is now working correctly:
- ✅ `npm run dev` loads `.env.local` with development settings
- ✅ `npm run dev:prod` loads `.env.prod` with production settings
- ✅ NODE_ENV is set correctly for each environment
- ✅ Different configurations are clearly visible in console output
- ✅ Icon loading works in both modes
