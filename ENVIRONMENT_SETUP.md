# Environment Configuration Setup

This project uses environment-specific configuration files to manage database connections, pCloud settings, and other application configurations.

## Environment Files

The project supports two environment files:

- **`.env.local`** - For local development
- **`.env.prod`** - For production deployment

## Setup Instructions

1. **Copy the example file:**
   ```bash
   cp .env.example .env.local
   ```

2. **Edit the configuration:**
   Open `.env.local` and update the values with your actual credentials:

   ```env
   # Database Configuration - Neon PostgreSQL
   DB_HOST=your-neon-host.neon.tech
   DB_PORT=5432
   DB_NAME=your_database_name
   DB_USER=your_database_user
   DB_PASSWORD=your_database_password
   DB_SSL=true

   # pCloud Configuration
   PCLOUD_USERNAME=<EMAIL>
   PCLOUD_PASSWORD=your_pcloud_password
   PCLOUD_REGION=us
   PCLOUD_REMOTE_FOLDER=/CSV_Backups
   PCLOUD_BASE_FOLDER=/csv_backup
   ```

3. **For production deployment:**
   ```bash
   cp .env.example .env.prod
   ```
   Then edit `.env.prod` with production-specific values.

## Configuration Categories

### Database Configuration
- `DB_HOST` - Neon PostgreSQL host
- `DB_PORT` - Database port (default: 5432)
- `DB_NAME` - Database name
- `DB_USER` - Database username
- `DB_PASSWORD` - Database password
- `DB_SSL` - Enable SSL connection (true/false)
- `DB_CONNECTION_TIMEOUT` - Connection timeout in milliseconds
- `DB_QUERY_TIMEOUT` - Query timeout in milliseconds

### pCloud Configuration
- `PCLOUD_USERNAME` - pCloud account email
- `PCLOUD_PASSWORD` - pCloud account password
- `PCLOUD_REGION` - pCloud region (us/eu)
- `PCLOUD_REMOTE_FOLDER` - Remote folder for CSV backups
- `PCLOUD_BASE_FOLDER` - Base folder for transaction backups
- `PCLOUD_TIMEOUT` - API request timeout in milliseconds
- `PCLOUD_UPLOAD_TIMEOUT` - File upload timeout in milliseconds

### CSV Processing Configuration
- `CSV_FOLDER_PATH` - Path to monitor for CSV files
- `CSV_BACKUP_PATH` - Path for local CSV backups
- `CSV_AUTO_PROCESSING` - Enable automatic processing (true/false)
- `CSV_PROCESS_DELAY` - Delay between processing in milliseconds
- `CSV_ENABLE_BACKUP` - Enable local backup (true/false)
- `CSV_ENABLE_CLEANUP` - Enable file cleanup after processing (true/false)
- `CSV_ENABLE_PCLOUD_BACKUP` - Enable pCloud backup (true/false)

### Application Configuration
- `NODE_ENV` - Environment mode (development/production)
- `APP_NAME` - Application name
- `APP_VERSION` - Application version
- `LOG_LEVEL` - Logging level (production only)
- `ENABLE_DEBUG` - Enable debug mode (production only)

## Environment Selection

The application automatically selects the appropriate environment file based on the `APP_ENV` variable:

- **Local Development** (`APP_ENV=local`): Uses `.env.local`
- **Production** (`APP_ENV=prod`): Uses `.env.prod`

If `APP_ENV` is not set, it falls back to `NODE_ENV` logic:
- **Development** (`NODE_ENV=development`): Uses `.env.local`
- **Production** (`NODE_ENV=production`): Uses `.env.prod`

## NPM Scripts

The project includes environment-specific npm scripts that set both `NODE_ENV` and `APP_ENV`:

### Development Scripts (uses .env.local)
```bash
npm run dev          # Start development server with .env.local
npm run start        # Preview build with .env.local
npm run build:dev    # Build for development with .env.local
npm run dist:dev     # Create distributable with .env.local
```

### Production Scripts (uses .env.prod)
```bash
npm run dev:prod     # Start development server with .env.prod
npm run build        # Build for production with .env.prod
npm run dist         # Create distributable with .env.prod
```

### Platform-Specific Builds
```bash
# Windows builds
npm run electron:build:win      # Production Windows build (.env.prod)
npm run electron:build:win:dev  # Development Windows build (.env.local)

# macOS builds
npm run electron:build:mac      # Production macOS build (.env.prod)
npm run electron:build:mac:dev  # Development macOS build (.env.local)
```

### Environment Variable Details
Each script sets the following environment variables:
- **Development scripts**: `NODE_ENV=development APP_ENV=local`
- **Production scripts**: `NODE_ENV=production APP_ENV=prod`

## Security Notes

1. **Never commit environment files** to version control
2. The `.env.local` and `.env.prod` files are already added to `.gitignore`
3. Use strong passwords for database and pCloud accounts
4. Regularly rotate credentials
5. Use different credentials for development and production

## Validation

The application validates required environment variables on startup and will log warnings if any are missing:

```
✅ Environment configuration is valid
```

or

```
⚠️ Missing required environment variables: ['DB_PASSWORD', 'PCLOUD_USERNAME']
```

## Troubleshooting

### Common Issues

1. **Missing environment file**: Copy `.env.example` to `.env.local`
2. **Invalid credentials**: Check database and pCloud credentials
3. **Connection timeouts**: Adjust timeout values in environment file
4. **Path issues**: Ensure CSV folder paths exist and are accessible

### Debug Information

The application logs configuration details on startup (sensitive data is masked):

```
🔧 Environment Configuration:
📱 App: Eposservice v0.0.0 (development)
🗄️ Database: your-host.neon.tech:5432/neondb
☁️ pCloud: <EMAIL> (us)
📁 CSV: /path/to/csv -> /path/to/backup
🔄 Auto Processing: true
☁️ pCloud Backup: false
```
