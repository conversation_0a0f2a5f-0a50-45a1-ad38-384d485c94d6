# Insert All Records Workflow

## Overview

The transaction processing system has been updated to **insert ALL records from CSV files**, including duplicates. This replaces the previous behavior of skipping duplicate transactions and ensures complete data preservation from source files.

## Key Changes

### 🔄 **Behavior Change**
- **Before**: Duplicate transactions were automatically detected and skipped
- **After**: All transactions from CSV files are processed and inserted, including duplicates

### 🗄️ **Database Schema Updates**
- Removed UNIQUE constraint on `transaction_id` column
- Added composite index for duplicate tracking: `(transaction_id, transaction_file_name, transaction_time)`
- Added `file_record_sequence` column for tracking record order within files
- Created `v_transaction_duplicates` view for duplicate analysis
- Added `get_transaction_history()` function for transaction tracking

### 📊 **Enhanced Logging**
Instead of:
```
📊 Transactions: 150 saved, 25 duplicates
```

Now provides:
```
📊 Transaction Summary:
   💾 Total Records Inserted: 175 transactions
   📝 Duplicates Detected: 25 transactions (all inserted)

📋 Duplicate Analysis (all records were inserted):
   📄 Same File Duplicates: 15 (re-processed from same file)
   📁 Cross File Duplicates: 10 (found in multiple files)
   💰 Exact Amount Matches: 20 (identical transaction amounts)
   💸 Amount Differences: 5 (different amounts for same transaction ID)
   🔍 Sample Duplicate IDs: 4200002645202507026000149512...
   ℹ️ Note: All duplicate transactions were inserted as separate records in the database
```

## Database Schema Changes

### Removed Constraints
```sql
-- Unique constraint removed to allow duplicates
-- ALTER TABLE transaction_e_pos DROP CONSTRAINT transaction_e_pos_transaction_id_key;
```

### New Indexes
```sql
-- Performance index for transaction_id lookups
CREATE INDEX idx_transaction_e_pos_transaction_id ON transaction_e_pos(transaction_id);

-- Composite index for duplicate tracking
CREATE INDEX idx_transaction_e_pos_duplicate_tracking 
ON transaction_e_pos(transaction_id, transaction_file_name, transaction_time);
```

### New Column
```sql
-- Track record sequence within file
ALTER TABLE transaction_e_pos 
ADD COLUMN file_record_sequence INTEGER;
```

### Duplicate Analysis View
```sql
CREATE VIEW v_transaction_duplicates AS
SELECT 
    transaction_id,
    COUNT(*) as occurrence_count,
    ARRAY_AGG(DISTINCT transaction_file_name) as source_files,
    ARRAY_AGG(DISTINCT transaction_amount) as amounts,
    MIN(transaction_time) as first_occurrence,
    MAX(transaction_time) as last_occurrence,
    MIN(create_dt) as first_inserted,
    MAX(create_dt) as last_inserted
FROM transaction_e_pos
GROUP BY transaction_id
HAVING COUNT(*) > 1
ORDER BY occurrence_count DESC, transaction_id;
```

### Transaction History Function
```sql
CREATE FUNCTION get_transaction_history(txn_id VARCHAR(100))
RETURNS TABLE (
    id BIGINT,
    transaction_amount DECIMAL(18,2),
    transaction_file_name TEXT,
    transaction_time TIMESTAMP,
    create_dt TIMESTAMP,
    file_record_sequence INTEGER
);
```

## Processing Workflow

### 1. File Processing
When processing CSV files, the system:
1. Parses all records from the file
2. Processes each transaction record
3. **Inserts ALL records** into the database
4. Logs duplicate detection for audit purposes

### 2. Duplicate Detection & Logging
For each transaction:
1. Checks if `transaction_id` already exists in database
2. If duplicate found:
   - Logs detailed duplicate information
   - **Still inserts the new record**
   - Records audit trail in `transaction_duplicate_log`
3. Provides detailed console output about duplicates

### 3. Enhanced Console Output
Per-file logging:
```
✅ File processed successfully: wechat_transactions_2025-07-19.csv
   📊 File Stats: 150 transactions processed, 150 records inserted

   📝 Duplicate Detection for wechat_transactions_2025-07-19.csv (all records inserted):
     1. Transaction ID: 4200002645202507026000149512
        Original Amount: ฿13,025.00 (wechat_transactions_2025-07-18.csv)
        New Amount: ฿13,025.00 (wechat_transactions_2025-07-19.csv)
        Amount Difference: ฿0.00
        Action: Both records inserted in database
        Files Match: No
```

## Benefits

### 1. **Complete Data Preservation**
- No data loss from CSV files
- All records are preserved in the database
- Historical data integrity maintained

### 2. **Audit Trail**
- Complete tracking of duplicate occurrences
- Source file tracking for each record
- Timestamp tracking for analysis

### 3. **Flexible Analysis**
- Duplicate analysis view for reporting
- Transaction history function for investigation
- Performance indexes for efficient queries

### 4. **Transparent Processing**
- Clear logging of what happened to each record
- Detailed duplicate analysis in console output
- No confusion about "missing" transactions

## Use Cases

### 1. **Re-processing Files**
When files need to be re-processed:
- All records are inserted again
- Duplicates are logged but not skipped
- Complete audit trail maintained

### 2. **Cross-File Analysis**
When same transactions appear in multiple files:
- All occurrences are preserved
- Source file tracking available
- Amount differences can be analyzed

### 3. **Data Reconciliation**
For financial reconciliation:
- All transaction records available
- Historical processing preserved
- Duplicate patterns can be analyzed

## Duplicate Analysis Queries

### Find All Duplicates
```sql
SELECT * FROM v_transaction_duplicates
ORDER BY occurrence_count DESC;
```

### Get Transaction History
```sql
SELECT * FROM get_transaction_history('4200002645202507026000149512');
```

### Analyze Amount Differences
```sql
SELECT 
    transaction_id,
    COUNT(*) as occurrences,
    COUNT(DISTINCT transaction_amount) as different_amounts,
    ARRAY_AGG(DISTINCT transaction_amount) as amounts
FROM transaction_e_pos
GROUP BY transaction_id
HAVING COUNT(DISTINCT transaction_amount) > 1
ORDER BY different_amounts DESC;
```

### Cross-File Duplicates
```sql
SELECT 
    transaction_id,
    COUNT(DISTINCT transaction_file_name) as file_count,
    ARRAY_AGG(DISTINCT transaction_file_name) as files
FROM transaction_e_pos
GROUP BY transaction_id
HAVING COUNT(DISTINCT transaction_file_name) > 1
ORDER BY file_count DESC;
```

## Migration Impact

### Backward Compatibility
- Existing code continues to work
- No breaking changes to API
- Enhanced logging provides more information

### Performance
- New indexes maintain query performance
- Composite index optimizes duplicate analysis
- View provides efficient duplicate reporting

### Data Integrity
- All historical data preserved
- No data loss during migration
- Enhanced audit capabilities

## Configuration

### Default Behavior
```typescript
// Default configuration now inserts all records
const defaultOptions = {
  strategy: 'INSERT_ALL',
  logDuplicates: true
};
```

### Custom Configuration
```typescript
// Can still be configured for different strategies if needed
const customOptions = {
  strategy: 'INSERT_ALL',
  logDuplicates: true,
  updateFields: [] // Not used for INSERT_ALL
};
```

## Summary

The "Insert All Records" workflow ensures:
- ✅ **Complete data preservation** - no records are skipped
- ✅ **Enhanced transparency** - detailed logging of all operations
- ✅ **Audit compliance** - complete tracking of duplicate occurrences
- ✅ **Flexible analysis** - tools for duplicate pattern analysis
- ✅ **Performance optimization** - efficient indexes for large datasets

This change transforms the system from a "skip duplicates" approach to a "preserve everything" approach, providing complete data integrity and transparency while maintaining performance and audit capabilities.
