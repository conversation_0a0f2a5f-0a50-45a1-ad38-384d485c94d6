# Merchant Hierarchy Feature - Parent/Sub Merchant Management

This document describes the implementation of the merchant hierarchy feature that allows sub merchants to be linked to their parent main merchants.

## Overview

The merchant hierarchy feature enables:
- **Main Merchants**: Independent merchant entities that can have sub merchants
- **Sub Merchants**: Dependent merchant entities that must be linked to a parent main merchant
- **Automatic Validation**: Ensures sub merchants always have a parent main merchant selected
- **Visual Indicators**: Clear display of parent-child relationships in the UI

## Database Changes

### New Column: `parent_merchant_id`
- **Type**: INTEGER (nullable)
- **Purpose**: Links sub merchants to their parent main merchants
- **Constraints**: 
  - Foreign key to `merchant.merchant_id`
  - NULL for main merchants
  - Required for sub merchants

### Database Migration
Run the migration script `database_migration_add_parent_merchant.sql` to add:
```sql
-- Add parent_merchant_id column
ALTER TABLE merchant ADD COLUMN parent_merchant_id INTEGER;

-- Add foreign key constraint
ALTER TABLE merchant 
ADD CONSTRAINT fk_merchant_parent 
FOREIGN KEY (parent_merchant_id) REFERENCES merchant(merchant_id);

-- Add check constraint for business rules
ALTER TABLE merchant 
ADD CONSTRAINT chk_merchant_parent_type 
CHECK (
  (merchant_type = 'main' AND parent_merchant_id IS NULL) OR
  (merchant_type = 'sub' AND parent_merchant_id IS NOT NULL)
);
```

## Backend Implementation

### Updated Merchant Interface
```typescript
interface Merchant {
  // ... existing fields
  parent_merchant_id?: number;
  parent_merchant_name?: string; // Populated via JOIN
  // ... rest of fields
}
```

### New IPC Handler: `get-main-merchants`
Returns list of active main merchants for dropdown selection:
```typescript
// Returns: { merchant_id: number, merchant_name: string }[]
const mainMerchants = await safeIpcInvoke('get-main-merchants');
```

### Updated Database Queries
- **GET merchants**: Now includes LEFT JOIN to fetch parent merchant names
- **CREATE merchant**: Handles `parent_merchant_id` field
- **UPDATE merchant**: Supports changing parent merchant relationships

## Frontend Implementation

### Enhanced Merchant Form
1. **Merchant Type Selection**: Dropdown with "Main Merchant" and "Sub Merchant" options
2. **Conditional Parent Selection**: 
   - Shows parent merchant dropdown only when "Sub Merchant" is selected
   - Dropdown populated with active main merchants
   - Required field validation for sub merchants
3. **Auto-clear Logic**: Clears parent selection when switching from sub to main type

### UI Components Updated

#### MerchantDetailsTab
- Added `mainMerchants` prop for parent merchant dropdown
- Conditional rendering based on merchant type
- Automatic form validation and clearing

#### Merchant Management Table
- New "Parent Merchant" column
- Shows parent merchant name for sub merchants
- Visual indicators for missing parent assignments

### Form Behavior

#### Creating New Merchant
1. Select merchant type (main/sub)
2. If "Sub Merchant" selected:
   - Parent merchant dropdown appears
   - Must select a parent main merchant
   - Form validation prevents submission without parent
3. If "Main Merchant" selected:
   - No parent selection required
   - Parent field automatically cleared

#### Editing Existing Merchant
1. Form pre-populated with current values including parent merchant
2. Can change merchant type (with appropriate validations)
3. Can change parent merchant for sub merchants
4. Cannot set parent for main merchants

## User Experience Features

### Visual Indicators
- **Type Badges**: Clear visual distinction between main and sub merchants
- **Parent Display**: Shows parent merchant name in table for sub merchants
- **Missing Parent Warning**: Red "Not Set" indicator for sub merchants without parents
- **Empty State Message**: Helpful message when no main merchants are available

### Validation Rules
1. **Sub merchants must have a parent**: Form validation prevents saving sub merchants without parent selection
2. **Main merchants cannot have parents**: Parent field is hidden and cleared for main merchants
3. **Parent must be active main merchant**: Dropdown only shows active main merchants
4. **Database constraints**: Server-side validation ensures data integrity

### Error Handling
- Graceful handling when no main merchants exist
- Clear error messages for validation failures
- Fallback displays for missing data

## API Endpoints

### Get Main Merchants
```typescript
// IPC: 'get-main-merchants'
// Returns: { success: boolean, data: MainMerchant[] }
interface MainMerchant {
  merchant_id: number;
  merchant_name: string;
}
```

### Create/Update Merchant
```typescript
// IPC: 'create-merchant' / 'update-merchant'
// Accepts: Merchant data including parent_merchant_id
interface MerchantData {
  // ... existing fields
  parent_merchant_id?: number; // Required for sub merchants
}
```

## Business Rules

1. **Main Merchants**:
   - Can exist independently
   - Can have multiple sub merchants
   - Cannot have a parent merchant
   - Can be converted to sub merchant (if business rules allow)

2. **Sub Merchants**:
   - Must have exactly one parent main merchant
   - Cannot have their own sub merchants
   - Can be converted to main merchant (parent relationship removed)
   - Inherit certain properties from parent (if implemented)

3. **Hierarchy Constraints**:
   - Only 2-level hierarchy supported (main → sub)
   - No circular references possible
   - Deleting main merchant sets sub merchants' parent to NULL (or prevents deletion)

## Testing Scenarios

### Create Sub Merchant
1. Select "Sub Merchant" type
2. Verify parent dropdown appears
3. Verify dropdown contains only active main merchants
4. Verify form validation requires parent selection
5. Verify successful creation with parent relationship

### Convert Merchant Types
1. Edit existing main merchant → change to sub merchant
2. Verify parent dropdown appears and is required
3. Edit existing sub merchant → change to main merchant
4. Verify parent field is hidden and cleared

### Table Display
1. Verify parent merchant names display correctly
2. Verify "Not Set" warning for orphaned sub merchants
3. Verify "-" display for main merchants

### Edge Cases
1. No main merchants available → helpful message displayed
2. Parent merchant becomes inactive → handle gracefully
3. Deleting parent merchant → handle sub merchant relationships

## Future Enhancements

1. **Multi-level Hierarchy**: Support for deeper nesting (sub-sub merchants)
2. **Bulk Operations**: Assign multiple sub merchants to a parent
3. **Inheritance Rules**: Sub merchants inherit settings from parents
4. **Reporting**: Hierarchical reports showing parent-child relationships
5. **Permissions**: Role-based access to parent merchant data

## Migration Guide

### For Existing Installations
1. Run database migration script
2. Update application code
3. Existing merchants will be main merchants by default
4. Manually assign parent relationships for existing sub merchants

### Data Cleanup
After migration, review existing merchants and:
1. Identify which should be sub merchants
2. Assign appropriate parent relationships
3. Verify data integrity with new constraints

This feature provides a robust foundation for managing merchant hierarchies while maintaining data integrity and providing an intuitive user experience.
