# Merchant Reference Field Implementation

## Overview

This document outlines the implementation of the `merchant_ref` field in the merchant management system. The field has been added to support external system integration and provide an additional reference identifier for merchants.

## Changes Made

### 1. Frontend Type Definition Updates

**File**: `src/renderer/types/merchant.ts`
- Added `merchant_ref?: string;` to the Merchant interface
- Field is optional to maintain backward compatibility

### 2. Frontend UI Updates

**File**: `src/renderer/screens/merchant-management.screen.tsx`
- Updated form data initialization in `handleCreate()` to include `merchant_ref: ""`
- Updated `handleEdit()` to include `merchant_ref: merchant.merchant_ref || ""`
- Updated `handleView()` to include `merchant_ref: merchant.merchant_ref || ""`
- Added "Reference" column to the merchants table
- Added table cell to display merchant_ref value

**File**: `src/renderer/components/merchant/MerchantDetailsTab.tsx`
- Added merchant_ref field to the read-only view section
- Added merchant_ref input field to the form section (between VAT and Sub Merchant Name)
- Added proper readOnly/disabled attributes for consistency
- Field includes placeholder text "Enter merchant reference"

### 3. Backend Handler Updates

**File**: `src/main/handler/merchantHandler.ts`
- Updated Merchant interface to include `merchant_ref?: string;`
- Updated `create-merchant` handler:
  - Added merchant_ref to INSERT query columns
  - Added merchant_ref to VALUES parameters
  - Added merchant_ref to RETURNING clause
- Updated `update-merchant` handler:
  - Added merchant_ref to UPDATE SET clause
  - Added merchant_ref to values array
  - Added merchant_ref to RETURNING clause
- Updated `get-merchants` handler:
  - Added merchant_ref to SELECT query
  - Added merchant_ref to search WHERE clause for searchability

### 4. Database Migration

**File**: `database_migration_add_merchant_ref.sql`
- Creates migration script to add `merchant_ref` column to merchant table
- Column type: `VARCHAR(255)`
- Includes safety check to prevent duplicate column creation
- Adds index for performance optimization
- Includes documentation comments

## Features Implemented

### ✅ Create Operation
- New merchants can have a merchant_ref value entered
- Field is optional and can be left empty
- Proper validation and form handling

### ✅ Update Operation
- Existing merchants can have their merchant_ref updated
- Field preserves existing values when editing
- Supports clearing the field (setting to empty)

### ✅ View Operation
- Merchant reference is displayed in read-only view
- Shows "-" when no reference is set
- Properly formatted in the details section

### ✅ Search Functionality
- Merchant reference is included in search queries
- Users can search by merchant reference
- Case-insensitive search support

### ✅ Table Display
- New "Reference" column in the merchants table
- Shows merchant_ref value or "-" if empty
- Properly aligned and formatted

## Database Schema

```sql
-- New column added to merchant table
ALTER TABLE merchant 
ADD COLUMN merchant_ref VARCHAR(255);

-- Index for performance
CREATE INDEX idx_merchant_merchant_ref ON merchant(merchant_ref);

-- Documentation
COMMENT ON COLUMN merchant.merchant_ref IS 'External reference field for merchant identification and integration with external systems';
```

## Usage Examples

### Creating a Merchant with Reference
```typescript
const newMerchant = {
  merchant_name: "Sample Merchant",
  merchant_ref: "EXT-REF-001",
  merchant_type: "main",
  active: true
};
```

### Searching by Reference
Users can now search for merchants using:
- Merchant name
- VAT number
- **Merchant reference** (new)
- Email
- Phone

### API Response
```json
{
  "merchant_id": 1,
  "merchant_name": "Sample Merchant",
  "merchant_vat": "1234567890",
  "merchant_ref": "EXT-REF-001",
  "merchant_type": "main",
  "active": true
}
```

## Migration Instructions

1. **Run Database Migration**:
   ```sql
   -- Execute the migration script
   \i database_migration_add_merchant_ref.sql
   ```

2. **Verify Migration**:
   ```sql
   -- Check if column exists
   SELECT column_name, data_type 
   FROM information_schema.columns 
   WHERE table_name = 'merchant' 
   AND column_name = 'merchant_ref';
   ```

3. **Test Functionality**:
   - Create new merchant with reference
   - Update existing merchant reference
   - Search by merchant reference
   - View merchant details

## Backward Compatibility

- ✅ Existing merchants without merchant_ref will show "-" in UI
- ✅ All existing functionality remains unchanged
- ✅ Optional field - no breaking changes to existing forms
- ✅ Database migration is safe and reversible

## Technical Notes

- Field is optional (`VARCHAR(255)` allows NULL)
- Indexed for performance optimization
- Included in search functionality
- Proper form validation and error handling
- Consistent with existing field patterns
- Follows established coding conventions

## Testing Checklist

- [ ] Create merchant with merchant_ref
- [ ] Create merchant without merchant_ref
- [ ] Update merchant_ref on existing merchant
- [ ] Clear merchant_ref (set to empty)
- [ ] Search by merchant_ref
- [ ] View merchant details with/without merchant_ref
- [ ] Table display shows merchant_ref correctly
- [ ] Form validation works properly
- [ ] Database operations complete successfully

## Future Enhancements

- Add validation rules for merchant_ref format
- Implement uniqueness constraints if required
- Add bulk import/export support for merchant_ref
- Consider integration with external systems using this reference
