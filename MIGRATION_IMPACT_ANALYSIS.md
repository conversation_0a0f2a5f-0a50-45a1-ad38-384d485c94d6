# Migration Impact Analysis: Dropping transaction_summary_report Table

## Overview
This document outlines the impact of dropping the `transaction_summary_report` table and keeping only `transaction_summary_report_detail`.

## Database Changes

### Tables Affected
- ✅ **transaction_summary_report** - Will be DROPPED
- ✅ **transaction_summary_report_detail** - Will be MODIFIED (add columns, remove FK)

### Views Affected
- ✅ **v_transaction_summary_workflow** - Will be DROPPED and replaced with `v_transaction_summary_detail_workflow`

### Functions Affected
- ✅ **get_merchant_summary_stats()** - Will be DROPPED (needs recreation if needed)

### Triggers Affected
- ✅ **trigger_update_transaction_summary_report_timestamp** - Will be DROPPED from main table

## Application Code Changes Required

### 1. Transaction Handler (`src/main/handler/transactionHandler.ts`)

#### IPC Handlers to Update:
- **get-transaction-summary-reports** (lines ~1200-1250)
  - Currently queries `transaction_summary_report` table
  - Needs to query `transaction_summary_report_detail` with GROUP BY batch_id

- **get-transaction-summary-report-details** (lines ~1255-1285)
  - Currently joins both tables
  - Needs to query only `transaction_summary_report_detail`

- **approve-transaction-summary-report** (lines ~1285-1315)
  - Currently updates `transaction_summary_report.status`
  - Needs to update status in detail table or remove this functionality

### 2. Transaction Summary Report Service (`src/main/services/transactionSummaryReportService.ts`)

#### Methods to Update:
- **saveReportToDatabase()** (lines ~550-610)
  - Currently inserts into both tables
  - Needs to insert only into detail table with batch metadata

- **generateSummaryFromUploadedTransactions()** (lines ~100-260)
  - Currently creates main report record
  - Needs to include batch metadata in each detail record

### 3. Frontend Components

#### Files to Update:
- **Transaction Summary Screen** - May need updates if it displays main report data
- **Report Components** - May need updates for data structure changes

## New Database Schema

### Updated transaction_summary_report_detail Table
```sql
-- New columns added:
batch_id VARCHAR(100)           -- Unique batch identifier
report_date DATE               -- Report date (from main table)
report_time TIME              -- Report time (from main table)  
running_number VARCHAR(50)     -- Running number (from main table)
processed_files TEXT[]        -- Array of processed files (from main table)

-- Existing columns remain the same
```

### New View: v_transaction_summary_detail_workflow
```sql
-- Replaces v_transaction_summary_workflow
-- Groups by batch_id instead of report_id
```

## Migration Steps

1. **Run Database Migration**
   ```bash
   psql "connection_string" -f database_migration_drop_transaction_summary_report.sql
   ```

2. **Update Application Code**
   - Modify transaction handler IPC methods
   - Update transaction summary report service
   - Test all transaction summary functionality

3. **Test Thoroughly**
   - Upload transaction files
   - Verify data is saved correctly in detail table only
   - Test transaction summary reports display
   - Verify transfer status functionality still works

## Risks and Considerations

### Data Loss Risk
- ⚠️ **HIGH RISK**: All existing data in `transaction_summary_report` will be lost
- ⚠️ Existing `report_id` foreign keys in detail table will become orphaned

### Functionality Impact
- Report approval workflow may need redesign
- Batch-level metadata needs to be duplicated in each detail record
- Queries will need to GROUP BY batch_id instead of using report_id

### Recommendations
1. **Backup existing data** before running migration
2. **Export current reports** if historical data is needed
3. **Update application code** before running migration
4. **Test thoroughly** in development environment first

## Alternative Approach
Consider keeping the main table but simplifying its structure instead of dropping it entirely, which would:
- Preserve existing functionality
- Maintain referential integrity
- Reduce code changes required
- Preserve historical data
