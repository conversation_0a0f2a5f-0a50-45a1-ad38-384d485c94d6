# pCloud CSV Backup Integration

This document describes the comprehensive pCloud backup integration for the CSV folder monitor, providing automatic cloud storage of processed CSV files.

## Overview

The pCloud backup feature extends the CSV folder monitor workflow to include automatic cloud storage, ensuring your CSV files are safely backed up to pCloud storage in addition to local backups.

## Features

- **Automatic pCloud Upload**: Files are automatically uploaded to pCloud during the CSV processing workflow
- **Local + Cloud Backup**: Maintains both local backups and cloud storage
- **Configurable Settings**: Full control over pCloud credentials and folder structure
- **Connection Testing**: Built-in connection testing to verify pCloud credentials
- **Batch Upload**: Upload existing backup files to pCloud in bulk
- **Error Handling**: Graceful error handling that doesn't interrupt the main workflow

## Backup Folder Paths

### Local Backup Path
```
/Users/<USER>/Desktop/simple_csv_bk
```

### pCloud Remote Path
```
/CSV_Backups (configurable)
```

## Configuration

### pCloud Settings
- **Username/Email**: Your pCloud account email
- **Password**: Your pCloud account password  
- **Region**: US (api.pcloud.com) or EU (eapi.pcloud.com)
- **Remote Folder**: Target folder in pCloud (default: /CSV_Backups)

### Default Configuration
```typescript
pcloudConfig: {
  username: '<EMAIL>',
  password: '********',
  region: 'us',
  remoteFolder: '/CSV_Backups'
}
```

## Workflow Integration

### Enhanced CSV Processing Workflow
1. **📁 File Detection**: New CSV file detected in simple_csv folder
2. **📖 Data Processing**: File content read and logged to console
3. **💾 Local Backup**: File copied to `/Users/<USER>/Desktop/simple_csv_bk`
4. **☁️ pCloud Upload**: File uploaded to pCloud (if enabled)
5. **🗑️ Cleanup**: Original file deleted (if cleanup enabled)

### Workflow Configuration Options
```typescript
interface WorkflowConfig {
  autoProcessing: boolean;        // Enable automatic processing
  processDelay: number;          // Delay between detection and processing
  enableBackup: boolean;         // Enable local backup creation
  enablePCloudBackup: boolean;   // Enable pCloud backup upload
  enableCleanup: boolean;        // Enable original file deletion
  pcloudConfig?: {
    username: string;
    password: string;
    region: 'us' | 'eu';
    remoteFolder: string;
  };
}
```

## User Interface

### pCloud Configuration Panel
The UI includes a dedicated pCloud configuration section that appears when pCloud backup is enabled:

#### Configuration Fields
- **Username/Email**: Input field for pCloud account email
- **Password**: Secure password input field
- **Region**: Dropdown to select US or EU region
- **Remote Folder**: Text input for target folder path

#### Status Display
- **Connection Status**: Shows connection test results
- **Account Information**: Displays email, premium status, quota information
- **Upload Results**: Shows results of batch upload operations

#### Action Buttons
- **Test Connection**: Verify pCloud credentials and connection
- **Upload Existing Backups**: Batch upload all files from local backup folder

### Workflow Steps Indicator
The workflow steps display now includes pCloud backup status:
- ✓ Create local backup copy (if enabled)
- ✓ Upload to pCloud (if enabled)
- ✓ Delete original file (if enabled)

## API Integration

### pCloud Service Class
```typescript
class PCloudService {
  // Authenticate with pCloud
  async authenticate(): Promise<PCloudAuthResult>
  
  // Create folder in pCloud
  async createFolder(folderPath: string): Promise<PCloudFolderResult>
  
  // Upload single file
  async uploadFile(localFilePath: string, remoteFolder: string): Promise<PCloudUploadResult>
  
  // Upload multiple files
  async uploadFiles(localFilePaths: string[], remoteFolder: string): Promise<BatchUploadResult>
  
  // Test connection
  async testConnection(): Promise<ConnectionTestResult>
}
```

### IPC Handlers
- `test-pcloud-csv-connection`: Test pCloud connection with current configuration
- `update-pcloud-config`: Update pCloud configuration settings
- `upload-backups-to-pcloud`: Upload all existing backup files to pCloud

## Error Handling

### Graceful Degradation
- pCloud upload failures don't interrupt the main CSV processing workflow
- Local backups continue to work even if pCloud is unavailable
- Detailed error logging for troubleshooting

### Common Error Scenarios
1. **Authentication Failures**: Invalid credentials or account issues
2. **Network Issues**: Connection timeouts or DNS resolution failures
3. **Storage Limits**: Account quota exceeded
4. **File Conflicts**: Files already exist in pCloud

### Error Recovery
- Automatic retry mechanisms for transient failures
- Clear error messages in the UI
- Fallback to local backup only when pCloud fails

## Security Considerations

### Credential Storage
- pCloud credentials are stored in the application configuration
- Passwords are handled securely in memory
- No credentials are logged or exposed in console output

### Data Privacy
- Files are uploaded directly to your personal pCloud account
- No third-party services involved in the backup process
- Full control over data location and access

## Usage Instructions

### Initial Setup
1. **Enable pCloud Backup**: Check the "pCloud Backup" option in workflow configuration
2. **Configure Credentials**: Enter your pCloud username and password
3. **Select Region**: Choose US or EU based on your account
4. **Set Remote Folder**: Specify the target folder in pCloud (default: /CSV_Backups)
5. **Test Connection**: Click "Test Connection" to verify settings

### Automatic Operation
Once configured and enabled:
- New CSV files will automatically be uploaded to pCloud during processing
- Local backups continue to be created as before
- Upload status is logged to the console

### Manual Operations
- **Upload Existing Backups**: Use the "Upload Existing Backups" button to sync previously backed up files
- **Test Connection**: Verify pCloud settings anytime with the test button
- **Monitor Status**: Check connection status and upload results in the UI

### Troubleshooting
1. **Connection Issues**: Verify internet connection and pCloud credentials
2. **Upload Failures**: Check account quota and file permissions
3. **Authentication Problems**: Ensure correct username/password and account status

## Benefits

1. **Data Safety**: Dual backup strategy (local + cloud) ensures data protection
2. **Automation**: Set-and-forget operation with automatic uploads
3. **Accessibility**: Access your CSV backups from anywhere via pCloud
4. **Scalability**: Cloud storage eliminates local disk space concerns
5. **Reliability**: Professional cloud storage with high availability

## Technical Implementation

### File Upload Process
1. **Authentication**: Obtain auth token from pCloud API
2. **Folder Creation**: Ensure target folder exists in pCloud
3. **File Upload**: Use multipart form upload to transfer file
4. **Verification**: Confirm successful upload and get file metadata

### Performance Optimization
- **Batch Operations**: Multiple files uploaded efficiently
- **Rate Limiting**: Controlled upload speed to avoid API limits
- **Timeout Handling**: Appropriate timeouts for large file uploads
- **Memory Management**: Streaming uploads for large files

This pCloud integration provides a robust, automated backup solution that seamlessly integrates with the existing CSV processing workflow while maintaining reliability and ease of use.
