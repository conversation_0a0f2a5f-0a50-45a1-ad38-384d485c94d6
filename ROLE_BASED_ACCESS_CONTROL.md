# Role-Based Access Control (RBAC) Implementation

## Overview

This document describes the comprehensive role-based access control system implemented for the Master Data sections of the Electron application. The system provides granular permissions based on user roles: **Admin**, **Maker**, and **View**.

## Role Definitions

### Admin Role (`admin`)
- **Full Access**: Complete control over all features
- **Permissions**: Create, Read, Update, Delete, Export
- **Special Access**: 
  - User Management
  - Company Settings
  - All Master Data sections

### Maker Role (`maker`)
- **Read/Write Access**: Can modify master data
- **Permissions**: Create, Read, Update, Delete, Export
- **Restrictions**: 
  - Cannot access User Management
  - Cannot modify Company Settings

### View Role (`view`)
- **Read-Only Access**: Can only view and export data
- **Permissions**: Read, Search, Filter, Export
- **Restrictions**: 
  - Cannot create, update, or delete records
  - Cannot access User Management
  - Cannot modify Company Settings

## Implementation Structure

### Core Files

#### 1. Role Access Hook (`src/renderer/hooks/useRoleAccess.ts`)
```typescript
// Main hook for role-based permissions
export function useRoleAccess()
export function useMasterDataAccess()
export function RoleBasedComponent()
export function usePermission()
```

#### 2. Export Utilities (`src/renderer/utils/exportUtils.ts`)
```typescript
// CSV/JSON export functionality for view role users
export function arrayToCSV()
export function downloadCSV()
export function downloadJSON()
```

#### 3. Role Information Components
- `src/renderer/components/MasterDataRoleInfo.tsx`
- `src/renderer/components/RoleAccessSummary.tsx`

### Updated Master Data Screens

#### 1. Bank Master (`src/renderer/screens/bank-master.screen.tsx`)
- ✅ Role-based Create/Update/Delete buttons
- ✅ Export functionality for all roles
- ✅ Role information display
- ✅ Permission checks in handlers

#### 2. Merchant Management (`src/renderer/screens/merchant-management.screen.tsx`)
- ✅ Role-based action buttons
- ✅ CSV export functionality
- ✅ Role information display
- ✅ Permission validation

#### 3. Network Service (`src/renderer/screens/network-service.screen.tsx`)
- ✅ Admin/Maker edit permissions
- ✅ JSON export for configuration
- ✅ Role information display
- ✅ Form submission validation

#### 4. Transaction Management (`src/renderer/screens/transaction-management.screen.tsx`)
- ✅ Upload restrictions for view role
- ✅ Process restrictions for view role
- ✅ Role-based UI feedback
- ✅ Permission checks

#### 5. Transaction List (`src/renderer/screens/transaction-list.screen.tsx`)
- ✅ Export restrictions based on role
- ✅ Role-based Excel export
- ✅ Permission validation

#### 6. Company Settings (`src/renderer/screens/company-settings.screen.tsx`)
- ✅ Admin-only access
- ✅ Edit restrictions for non-admin users
- ✅ Role information display
- ✅ Permission validation

## Permission Matrix

| Feature | Admin | Maker | View |
|---------|-------|-------|------|
| **Master Data - View** | ✅ | ✅ | ✅ |
| **Master Data - Search/Filter** | ✅ | ✅ | ✅ |
| **Master Data - Export** | ✅ | ✅ | ✅ |
| **Master Data - Create** | ✅ | ✅ | ❌ |
| **Master Data - Update** | ✅ | ✅ | ❌ |
| **Master Data - Delete** | ✅ | ✅ | ❌ |
| **Transaction Upload** | ✅ | ✅ | ❌ |
| **Transaction Process** | ✅ | ✅ | ❌ |
| **Company Settings** | ✅ | ❌ | ❌ |
| **User Management** | ✅ | ❌ | ❌ |

## Usage Examples

### 1. Using Role-Based Components
```typescript
import { RoleBasedComponent } from '../hooks/useRoleAccess';

// Show button only for users with create permission
<RoleBasedComponent requiredPermission="canCreate">
  <Button onClick={handleCreate}>Add New</Button>
</RoleBasedComponent>

// Show different content for read-only users
<RoleBasedComponent 
  requiredPermission="canUpdate"
  fallback={<span>View Only</span>}
>
  <Button onClick={handleEdit}>Edit</Button>
</RoleBasedComponent>
```

### 2. Permission Checks in Functions
```typescript
import { useMasterDataAccess } from '../hooks/useRoleAccess';

function MyComponent() {
  const { canCreate, canUpdate, canDelete } = useMasterDataAccess();
  
  const handleCreate = () => {
    if (!canCreate) {
      showNotification("You don't have permission to create records", "error");
      return;
    }
    // Proceed with create logic
  };
}
```

### 3. Export Functionality
```typescript
import { downloadCSV, BANK_EXPORT_HEADERS } from '../utils/exportUtils';

const handleExport = () => {
  if (!canExport) {
    alert("You don't have permission to export data");
    return;
  }
  
  downloadCSV(data, 'banks.csv', BANK_EXPORT_HEADERS);
};
```

## Security Features

### 1. Frontend Validation
- Permission checks in UI components
- Role-based button visibility
- Function-level permission validation
- User feedback for restricted actions

### 2. Backend Integration
- User role stored in authentication context
- Session-based role validation
- Database-level user role management

### 3. Visual Feedback
- Role information display in headers
- Disabled buttons for restricted actions
- "View Only" indicators
- Permission-based styling

## Testing

### Role Demo Screen (`/role-demo`)
A comprehensive demonstration screen showcasing:
- Role information display
- Permission matrix visualization
- Interactive button testing
- Admin-only section demonstration

### Test Scenarios
1. **Admin User**: Should have access to all features
2. **Maker User**: Should be able to modify master data but not access admin features
3. **View User**: Should only be able to view and export data

## Future Enhancements

### Potential Improvements
1. **Granular Permissions**: Section-specific permissions (e.g., can edit banks but not merchants)
2. **Time-based Access**: Temporary permission elevation
3. **Audit Logging**: Track permission-based actions
4. **Dynamic Roles**: Runtime role assignment
5. **Resource-level Permissions**: Record-specific access control

### Additional Features
1. **Permission Groups**: Combine multiple permissions into groups
2. **Conditional Access**: Time or location-based restrictions
3. **Approval Workflows**: Multi-step approval for sensitive actions
4. **Permission Inheritance**: Hierarchical permission structure

## Maintenance

### Adding New Permissions
1. Update `useRoleAccess.ts` with new permission
2. Add permission to role matrix
3. Implement UI components with new permission
4. Update documentation

### Adding New Roles
1. Define role in authentication system
2. Update `useRoleAccess.ts` role definitions
3. Update permission matrix
4. Test all affected components

## Conclusion

The role-based access control system provides a robust, scalable foundation for managing user permissions across the Master Data sections. The implementation ensures security while maintaining a good user experience through clear visual feedback and intuitive permission handling.
