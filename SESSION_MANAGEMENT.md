# Session Management with 1-Hour Timeout

This document describes the comprehensive session management system implemented in the Electron desktop application with PostgreSQL authentication and 1-hour session expiry timeout.

## Overview

The session management system provides:
- **1-hour session timeout** with automatic expiry
- **Real-time session monitoring** every 5 minutes
- **Session timeout warnings** 10 minutes before expiry
- **Session extension capabilities** for active users
- **Automatic logout** when sessions expire
- **Single session per user** enforcement
- **Server-side session validation** with database tracking

## Architecture

### Backend Components

#### 1. Authentication Handler (`src/main/handler/authHandler.ts`)
- Handles user login, logout, and session validation
- Implements 1-hour session expiry (line 270: `1 * 60 * 60 * 1000`)
- Tracks sessions in `log_history_user` table
- Enforces single session per user with force login option
- Validates sessions using Thailand timezone utilities

#### 2. Timezone Utilities (`src/main/utils/timezone.ts`)
- `isSessionExpired()`: Checks if session has expired based on 1-hour timeout
- `getCurrentThailandDate()`: Gets current time in Thailand timezone
- `getSessionExpiryRange()`: Creates SQL conditions for session expiry queries

### Frontend Components

#### 1. AuthContext (`src/renderer/contexts/AuthContext.tsx`)
- Manages authentication state and session data
- Implements automatic session monitoring every 5 minutes
- Provides session extension and validation functions
- Handles automatic logout on session expiry
- Persists session data in localStorage with expiry

#### 2. Session UI Components

##### SessionTimeoutWarning (`src/renderer/components/SessionTimeoutWarning.tsx`)
- Shows warning popup 10 minutes before session expiry
- Provides "Extend Session" and "Logout Now" buttons
- Updates countdown timer in real-time
- Automatically dismisses when session is extended

##### SessionStatusIndicator (`src/renderer/components/SessionStatusIndicator.tsx`)
- Displays current user info and session time remaining
- Color-coded status (green/yellow/red based on time remaining)
- Available in full and compact modes
- Includes SessionTimer and SessionStatusBadge variants

#### 3. Protected Routes (`src/renderer/components/ProtectedRoute.tsx`)
- Validates session on route access
- Redirects to login if session is invalid
- Supports role-based access control

## Key Features

### 1. Session Timeout (1 Hour)
```typescript
// Backend: Session expiry time
const expiresAt = new Date(Date.now() + 1 * 60 * 60 * 1000); // 1 hour

// Frontend: Check if session expired
const isExpired = isSessionExpired(sessionTime, 1); // 1 hour
```

### 2. Automatic Monitoring
```typescript
// Check session every 5 minutes
const interval = setInterval(async () => {
  const isValid = await validateSessionWithServer(userId, sessionId);
  if (!isValid) {
    await logout();
  }
}, 5 * 60 * 1000);
```

### 3. Session Extension
```typescript
const extendSession = async (): Promise<boolean> => {
  const isValid = await validateSessionWithServer(userId, sessionId);
  if (isValid) {
    const newExpiresAt = new Date(Date.now() + 1 * 60 * 60 * 1000);
    // Update localStorage and state
    return true;
  }
  return false;
};
```

### 4. Timeout Warning
```typescript
// Show warning when 10 minutes or less remain
const warningThresholdMinutes = 10;
const shouldShowWarning = minutesRemaining <= warningThresholdMinutes && minutesRemaining > 0;
```

## Database Schema

### Session Tracking Table
```sql
-- log_history_user table tracks all session activities
CREATE TABLE log_history_user (
  id SERIAL PRIMARY KEY,
  user_id INTEGER NOT NULL,
  user_name VARCHAR(255) NOT NULL,
  action_type VARCHAR(50) NOT NULL, -- 'LOGIN_SUCCESS', 'LOGOUT', 'FORCE_LOGOUT'
  session_id UUID,
  ip_address VARCHAR(45),
  user_agent TEXT,
  failure_reason VARCHAR(255),
  create_dt TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Session Validation Query
```sql
-- Check if session is active (not expired and not logged out)
SELECT * FROM log_history_user 
WHERE user_id = $1 
  AND session_id = $2 
  AND action_type = 'LOGIN_SUCCESS'
  AND create_dt > (CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Bangkok' - INTERVAL '1 hours')
  AND NOT EXISTS (
    SELECT 1 FROM log_history_user logout_log
    WHERE logout_log.user_id = $1
      AND logout_log.session_id = $2
      AND logout_log.action_type IN ('LOGOUT', 'FORCE_LOGOUT')
      AND logout_log.create_dt > log_history_user.create_dt
  );
```

## Usage Examples

### 1. Basic Session Management
```typescript
import { useAuth } from '../contexts/AuthContext';

function MyComponent() {
  const { isAuthenticated, user, logout, validateSession } = useAuth();
  
  // Check if user is authenticated
  if (!isAuthenticated) {
    return <div>Please login</div>;
  }
  
  return <div>Welcome, {user?.user_name}!</div>;
}
```

### 2. Session Status Display
```typescript
import { SessionStatusIndicator } from '../components/SessionStatusIndicator';

function Header() {
  return (
    <header>
      <SessionStatusIndicator 
        showTimeRemaining={true}
        compact={false}
      />
    </header>
  );
}
```

### 3. Session Timeout Warning
```typescript
import { SessionTimeoutWarning } from '../components/SessionTimeoutWarning';

function App() {
  return (
    <div>
      {/* Your app content */}
      <SessionTimeoutWarning warningThresholdMinutes={10} />
    </div>
  );
}
```

### 4. Custom Session Monitoring
```typescript
import { useSessionTimeRemaining, useSessionTimeoutWarning } from '../components/SessionTimeoutWarning';

function CustomSessionDisplay() {
  const timeRemaining = useSessionTimeRemaining();
  const { shouldShowWarning, minutesRemaining } = useSessionTimeoutWarning(15);
  
  return (
    <div>
      {timeRemaining && (
        <span>Session expires in: {Math.floor(timeRemaining / 60000)} minutes</span>
      )}
      {shouldShowWarning && (
        <div className="warning">Session expiring in {minutesRemaining} minutes!</div>
      )}
    </div>
  );
}
```

## Configuration

### Session Timeout Duration
To change the session timeout duration, update these values:

1. **Backend** (`src/main/handler/authHandler.ts`):
```typescript
const expiresAt = new Date(Date.now() + 2 * 60 * 60 * 1000); // 2 hours
```

2. **Session validation** (`src/main/utils/timezone.ts`):
```typescript
export function isSessionExpired(sessionCreateTime: Date, expiryHours: number = 2): boolean
```

3. **Frontend extension** (`src/renderer/contexts/AuthContext.tsx`):
```typescript
const newExpiresAt = new Date(Date.now() + 2 * 60 * 60 * 1000); // 2 hours
```

### Monitoring Intervals
- **Session monitoring**: Every 5 minutes (configurable in AuthContext)
- **Warning threshold**: 10 minutes before expiry (configurable in components)
- **Timer updates**: Every 30 seconds for warnings, every second for precise timers

## Security Features

1. **Server-side validation**: All session checks are verified with the database
2. **Single session enforcement**: Only one active session per user
3. **Automatic cleanup**: Expired sessions are automatically invalidated
4. **Secure storage**: Session data includes expiry timestamps
5. **Force logout**: New logins can terminate existing sessions
6. **Audit trail**: All session activities are logged in the database

## Demo Page

Visit `/session-demo` in the application to see:
- Current session information
- Real-time session countdown
- Session management controls
- UI component examples
- Technical implementation details

The demo page provides a comprehensive overview of all session management features and allows testing of session extension and validation functionality.
