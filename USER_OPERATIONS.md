# Automatic User Field Management System

This document describes the comprehensive user field management system that automatically handles `create_by` and `update_by` fields using the current authenticated user's username (`user?.user_name`).

## Overview

The system provides automatic injection of user information into database operations, eliminating the need to manually set `create_by` and `update_by` fields throughout the application.

## Key Features

- **Automatic User Field Injection**: Automatically adds `create_by` and `update_by` fields
- **Current User Context**: Uses authenticated user's username from `useAuth()` hook
- **Type-Safe Operations**: Full TypeScript support with proper typing
- **Specialized Hooks**: Domain-specific hooks for different data types
- **Consistent API**: Unified interface across all CRUD operations

## Architecture

### Core Components

#### 1. `useCurrentUser` Hook (`src/renderer/hooks/useCurrentUser.ts`)
Provides utilities for working with current user information:

```typescript
const { 
  currentUserName,           // Current user's username
  withCreateBy,             // Add create_by field
  withUpdateBy,             // Add update_by field
  withUserFields,           // Add both create_by and update_by
  createFormData,           // Initialize form for creating
  updateFormData            // Initialize form for updating
} = useCurrentUser();
```

#### 2. `useUserOperations` Hook (`src/renderer/hooks/useUserOperations.ts`)
Comprehensive hook for database operations with automatic user field injection:

```typescript
const {
  // Data preparation
  prepareCreateData,        // Prepare data for creation
  prepareUpdateData,        // Prepare data for updates
  
  // Database operations
  createRecord,             // Create with auto create_by
  updateRecord,             // Update with auto update_by
  saveRecord,               // Create or update based on context
  
  // Batch operations
  createRecords,            // Batch create
  updateRecords,            // Batch update
  
  // Direct IPC access
  ipc                       // IPC utilities with user injection
} = useUserOperations();
```

#### 3. Specialized Domain Hooks
- `useMerchantOperations()`: Merchant-specific operations
- `useBankOperations()`: Bank master operations  
- `useUserManagementOperations()`: User management operations

## Usage Examples

### 1. Basic User Field Injection

```typescript
import { useCurrentUser } from '../hooks/useCurrentUser';

function MyComponent() {
  const { prepareCreateData, prepareUpdateData } = useCurrentUser();
  
  // For creating new records
  const newRecord = prepareCreateData({
    name: "Sample Record",
    active: true
  });
  // Result: { name: "Sample Record", active: true, create_by: "john_doe" }
  
  // For updating existing records
  const updateData = prepareUpdateData({
    name: "Updated Record",
    active: false
  });
  // Result: { name: "Updated Record", active: false, update_by: "john_doe" }
}
```

### 2. Database Operations with Auto User Fields

```typescript
import { useUserOperations } from '../hooks/useUserOperations';

function DataManagement() {
  const { createRecord, updateRecord, saveRecord } = useUserOperations();
  
  // Create new record (create_by automatically added)
  const handleCreate = async () => {
    const result = await createRecord('create-merchant', {
      merchant_name: "New Merchant",
      active: true
    });
    // IPC call will include: create_by: "current_username"
  };
  
  // Update existing record (update_by automatically added)
  const handleUpdate = async (id: number) => {
    const result = await updateRecord('update-merchant', id, {
      merchant_name: "Updated Merchant",
      active: false
    });
    // IPC call will include: update_by: "current_username"
  };
  
  // Smart save (create or update based on context)
  const handleSave = async (data: any, isEdit: boolean, id?: number) => {
    const result = await saveRecord(
      'create-merchant',    // Create channel
      'update-merchant',    // Update channel
      data,                 // Data object
      isEdit,              // Is this an update?
      id                   // ID for updates
    );
    // Automatically adds create_by or update_by based on isEdit
  };
}
```

### 3. Specialized Domain Operations

```typescript
import { useMerchantOperations } from '../hooks/useUserOperations';

function MerchantManagement() {
  const { 
    createMerchant, 
    updateMerchant, 
    createMerchantBank,
    prepareCreateData,
    prepareUpdateData 
  } = useMerchantOperations();
  
  // All operations automatically include user fields
  const handleCreateMerchant = async () => {
    const result = await createMerchant({
      merchant_name: "New Merchant",
      merchant_type: "main",
      active: true
    });
    // create_by automatically added
  };
  
  const handleUpdateMerchant = async (id: number) => {
    const result = await updateMerchant(id, {
      merchant_name: "Updated Merchant",
      active: false
    });
    // update_by automatically added
  };
}
```

### 4. Form Data Initialization

```typescript
import { useCurrentUser } from '../hooks/useCurrentUser';

function FormComponent() {
  const { prepareCreateData, prepareUpdateData } = useCurrentUser();
  
  // Initialize form for creating new record
  const [formData, setFormData] = useState(() => 
    prepareCreateData({
      name: "",
      active: true,
      description: ""
    })
  );
  
  // Initialize form for editing existing record
  const handleEdit = (existingRecord: any) => {
    setFormData(prepareUpdateData({
      name: existingRecord.name,
      active: existingRecord.active,
      description: existingRecord.description,
      create_by: existingRecord.create_by // Preserve original creator
    }));
  };
}
```

## Migration Guide

### Before (Manual User Field Management)

```typescript
// Old way - manual user field injection
const { user } = useAuth();

const handleCreate = async () => {
  const result = await safeIpcInvoke('create-merchant', {
    merchant_name: "New Merchant",
    active: true,
    create_by: user?.user_name || 'SYSTEM'  // Manual injection
  });
};

const handleUpdate = async (id: number) => {
  const result = await safeIpcInvoke('update-merchant', id, {
    merchant_name: "Updated Merchant", 
    active: false,
    update_by: user?.user_name || 'SYSTEM'  // Manual injection
  });
};
```

### After (Automatic User Field Management)

```typescript
// New way - automatic user field injection
const { createMerchant, updateMerchant } = useMerchantOperations();

const handleCreate = async () => {
  const result = await createMerchant({
    merchant_name: "New Merchant",
    active: true
    // create_by automatically added
  });
};

const handleUpdate = async (id: number) => {
  const result = await updateMerchant(id, {
    merchant_name: "Updated Merchant",
    active: false
    // update_by automatically added
  });
};
```

## Implementation Status

### ✅ Completed Components
- **Merchant Management**: Fully migrated to use `useMerchantOperations()`
- **Bank Master**: Fully migrated to use `useBankOperations()`
- **User Management**: Fully migrated to use `useUserManagementOperations()`
- **Session Demo**: Updated to demonstrate user operations

### 🔄 Components to Migrate
- Company Settings
- Network Service Management
- Any other CRUD components

## Benefits

1. **Consistency**: All records automatically track who created/updated them
2. **Reduced Errors**: No more forgetting to add user fields
3. **Cleaner Code**: Less boilerplate in components
4. **Type Safety**: Full TypeScript support prevents field mismatches
5. **Maintainability**: Centralized user field logic
6. **Audit Trail**: Complete tracking of data changes

## Best Practices

1. **Use Specialized Hooks**: Prefer domain-specific hooks like `useMerchantOperations()`
2. **Consistent Naming**: Always use `create_by` and `update_by` field names
3. **Preserve Original Creator**: When updating, preserve the original `create_by` value
4. **Form Initialization**: Use `prepareCreateData()` and `prepareUpdateData()` for forms
5. **Batch Operations**: Use batch functions for multiple record operations

## Error Handling

The system gracefully handles cases where user information is not available:

```typescript
// Falls back to 'SYSTEM' if user is not authenticated
create_by: currentUser?.user_name || 'SYSTEM'
update_by: currentUser?.user_name || 'SYSTEM'
```

## Testing

Visit `/session-demo` in the application to see:
- Current user information display
- Examples of automatic user field injection
- Live demonstration of data preparation utilities
- Real-time user context information

The demo page shows how the system automatically injects user fields into data objects, making it easy to verify the functionality is working correctly.
