-- Migration script to add parent_merchant_id column to merchant table
-- This enables sub merchants to be linked to their parent main merchants

-- Add parent_merchant_id column to merchant table
ALTER TABLE merchant 
ADD COLUMN parent_merchant_id INTEGER;

-- Add foreign key constraint to ensure parent_merchant_id references a valid merchant
ALTER TABLE merchant 
ADD CONSTRAINT fk_merchant_parent 
FOREIGN KEY (parent_merchant_id) 
REFERENCES merchant(merchant_id) 
ON DELETE SET NULL;

-- Add check constraint to ensure only sub merchants can have a parent
ALTER TABLE merchant 
ADD CONSTRAINT chk_merchant_parent_type 
CHECK (
  (merchant_type = 'main' AND parent_merchant_id IS NULL) OR
  (merchant_type = 'sub' AND parent_merchant_id IS NOT NULL)
);

-- Create index for better performance on parent_merchant_id lookups
CREATE INDEX idx_merchant_parent_merchant_id ON merchant(parent_merchant_id);

-- Add comment to document the column purpose
COMMENT ON COLUMN merchant.parent_merchant_id IS 'References the parent merchant for sub merchants. NULL for main merchants.';

-- Verify the changes
SELECT 
  column_name, 
  data_type, 
  is_nullable, 
  column_default
FROM information_schema.columns 
WHERE table_name = 'merchant' 
  AND column_name = 'parent_merchant_id';

-- Show table constraints
SELECT 
  constraint_name, 
  constraint_type, 
  table_name
FROM information_schema.table_constraints 
WHERE table_name = 'merchant' 
  AND constraint_name LIKE '%parent%';
