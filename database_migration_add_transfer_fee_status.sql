-- Migration: Add is_transfer field to transaction_summary_report_detail table
-- Date: 2025-07-19
-- Description: Adds transfer status tracking to transaction summary report details

-- Add the new is_transfer column
ALTER TABLE transaction_summary_report_detail
ADD COLUMN IF NOT EXISTS is_transfer SMALLINT DEFAULT 0;

-- Add check constraint to ensure valid values (0 or 1)
ALTER TABLE transaction_summary_report_detail
ADD CONSTRAINT IF NOT EXISTS chk_is_transfer_valid
CHECK (is_transfer IN (0, 1));

-- Add index for the new field to optimize queries
CREATE INDEX IF NOT EXISTS idx_transaction_summary_report_detail_transfer
ON transaction_summary_report_detail(is_transfer);

-- Add comment for documentation
COMMENT ON COLUMN transaction_summary_report_detail.is_transfer IS 'Transfer status: 0 = not yet transferred, 1 = transfer already';

-- Update existing records to default value (0 = not yet transferred)
UPDATE transaction_summary_report_detail
SET is_transfer = 0
WHERE is_transfer IS NULL;

-- Make the column NOT NULL after setting default values
ALTER TABLE transaction_summary_report_detail
ALTER COLUMN is_transfer SET NOT NULL;

-- Verify the migration
SELECT 
    column_name, 
    data_type, 
    is_nullable, 
    column_default
FROM information_schema.columns 
WHERE table_name = 'transaction_summary_report_detail'
AND column_name = 'is_transfer';

-- Show sample data with the new field
SELECT 
    id, 
    merchant_vat, 
    merchant_name, 
    final_net_amount,
    is_transfer,
    CASE
        WHEN is_transfer = 0 THEN 'Not Yet Transferred'
        WHEN is_transfer = 1 THEN 'Transfer Already'
        ELSE 'Unknown Status'
    END as transfer_status_description
FROM transaction_summary_report_detail 
ORDER BY id 
LIMIT 5;
