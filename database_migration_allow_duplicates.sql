-- Migration: Allow Duplicate Transactions
-- Date: 2025-07-19
-- Description: Remove UNIQUE constraint on transaction_id to allow duplicate transactions

-- Step 1: Check if the unique constraint exists and drop it
DO $$
BEGIN
    -- Check if the unique constraint exists
    IF EXISTS (
        SELECT 1 
        FROM information_schema.table_constraints 
        WHERE constraint_name = 'transaction_e_pos_transaction_id_key' 
        AND table_name = 'transaction_e_pos'
    ) THEN
        -- Drop the unique constraint
        ALTER TABLE transaction_e_pos DROP CONSTRAINT transaction_e_pos_transaction_id_key;
        RAISE NOTICE 'Dropped unique constraint on transaction_id';
    ELSE
        RAISE NOTICE 'Unique constraint on transaction_id does not exist or already dropped';
    END IF;
END $$;

-- Step 2: Keep the index for performance but remove uniqueness
DROP INDEX IF EXISTS idx_transaction_e_pos_transaction_id;
CREATE INDEX IF NOT EXISTS idx_transaction_e_pos_transaction_id ON transaction_e_pos(transaction_id);

-- Step 3: Add a new composite index for better duplicate tracking
CREATE INDEX IF NOT EXISTS idx_transaction_e_pos_duplicate_tracking 
ON transaction_e_pos(transaction_id, transaction_file_name, transaction_time);

-- Step 4: Add a new field to track record sequence within file (optional)
ALTER TABLE transaction_e_pos 
ADD COLUMN IF NOT EXISTS file_record_sequence INTEGER;

-- Step 5: Add comment to document the change
COMMENT ON COLUMN transaction_e_pos.transaction_id IS 'Transaction identifier - duplicates are now allowed to support multiple file processing';
COMMENT ON INDEX idx_transaction_e_pos_duplicate_tracking IS 'Composite index for tracking duplicate transactions across files';

-- Step 6: Create a view for duplicate analysis
CREATE OR REPLACE VIEW v_transaction_duplicates AS
SELECT 
    transaction_id,
    COUNT(*) as occurrence_count,
    ARRAY_AGG(DISTINCT transaction_file_name) as source_files,
    ARRAY_AGG(DISTINCT transaction_amount) as amounts,
    MIN(transaction_time) as first_occurrence,
    MAX(transaction_time) as last_occurrence,
    MIN(create_dt) as first_inserted,
    MAX(create_dt) as last_inserted
FROM transaction_e_pos
GROUP BY transaction_id
HAVING COUNT(*) > 1
ORDER BY occurrence_count DESC, transaction_id;

-- Step 7: Create function to get transaction history
CREATE OR REPLACE FUNCTION get_transaction_history(txn_id VARCHAR(100))
RETURNS TABLE (
    id BIGINT,
    transaction_amount DECIMAL(18,2),
    transaction_file_name TEXT,
    transaction_time TIMESTAMP,
    create_dt TIMESTAMP,
    file_record_sequence INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        t.id,
        t.transaction_amount,
        t.transaction_file_name,
        t.transaction_time,
        t.create_dt,
        t.file_record_sequence
    FROM transaction_e_pos t
    WHERE t.transaction_id = txn_id
    ORDER BY t.create_dt ASC;
END;
$$ LANGUAGE plpgsql;

-- Step 8: Verification queries
SELECT 'Migration completed successfully' as status;

-- Show current constraints on the table
SELECT 
    constraint_name, 
    constraint_type,
    is_deferrable,
    initially_deferred
FROM information_schema.table_constraints 
WHERE table_name = 'transaction_e_pos'
AND constraint_type IN ('UNIQUE', 'PRIMARY KEY')
ORDER BY constraint_name;

-- Show indexes on transaction_id
SELECT 
    indexname,
    indexdef
FROM pg_indexes 
WHERE tablename = 'transaction_e_pos'
AND indexdef LIKE '%transaction_id%'
ORDER BY indexname;

-- Sample duplicate analysis (if any duplicates exist)
SELECT 
    'Duplicate Analysis' as info,
    COUNT(DISTINCT transaction_id) as unique_transactions,
    COUNT(*) as total_records,
    COUNT(*) - COUNT(DISTINCT transaction_id) as duplicate_records
FROM transaction_e_pos;

-- Show sample of potential duplicates (if any exist)
SELECT 
    transaction_id,
    COUNT(*) as count,
    ARRAY_AGG(DISTINCT transaction_file_name) as files
FROM transaction_e_pos
GROUP BY transaction_id
HAVING COUNT(*) > 1
LIMIT 5;
