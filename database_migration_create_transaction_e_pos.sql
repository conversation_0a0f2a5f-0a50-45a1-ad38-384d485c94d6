-- Migration: Create transaction_e_pos table for e-commerce transaction management
-- Date: 2025-07-16
-- Description: Creates the main transaction table with comprehensive fields for transaction processing

-- Create transaction_e_pos table
CREATE TABLE IF NOT EXISTS transaction_e_pos (
    id BIGSERIAL PRIMARY KEY,
    reference_no VARCHAR(100),
    transaction_id VARCHAR(100) UNIQUE,
    transaction_out_id VARCHAR(100),
    transaction_card_no VARCHAR(100),
    transaction_merchant_id VARCHAR(100),
    transaction_merchant_name VARCHAR(100),
    transaction_merchant_vat VARCHAR(100),
    transaction_time TIMESTAMP,
    transaction_amount DECIMAL(18,2), -- refund as negative
    transaction_refund_id VARCHAR(100),
    transaction_refund_out_id VARCHAR(100),
    transaction_mch_id VARCHAR(100),
    transaction_sub_mch_id VARCHAR(50), -- TID
    transaction_trade_type VARCHAR(50), -- e.g., 'micropay'
    transaction_trade_status VARCHAR(50), -- e.g., 'success', 'refund'
    transaction_bank_type VARCHAR(50), -- e.g., 'CMB_CREDIT'
    transaction_fee_type VARCHAR(50), -- e.g., 'THB'
    transaction_coupon_amount DECIMAL(18,2),
    transaction_file_name TEXT,
    transaction_file_name_backup TEXT,
    transaction_channel_type VARCHAR(50), -- e.g., 'WeChat', 'UNIPAY'
    
    -- Standard audit columns
    create_by VARCHAR(100) DEFAULT 'SYSTEM',
    create_dt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_by VARCHAR(100) DEFAULT 'SYSTEM',
    update_dt TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for performance optimization
CREATE INDEX IF NOT EXISTS idx_transaction_e_pos_transaction_id ON transaction_e_pos(transaction_id);
CREATE INDEX IF NOT EXISTS idx_transaction_e_pos_reference_no ON transaction_e_pos(reference_no);
CREATE INDEX IF NOT EXISTS idx_transaction_e_pos_card_no ON transaction_e_pos(transaction_card_no);
CREATE INDEX IF NOT EXISTS idx_transaction_e_pos_merchant_id ON transaction_e_pos(transaction_merchant_id);
CREATE INDEX IF NOT EXISTS idx_transaction_e_pos_merchant_vat ON transaction_e_pos(transaction_merchant_vat);
CREATE INDEX IF NOT EXISTS idx_transaction_e_pos_sub_mch_id ON transaction_e_pos(transaction_sub_mch_id);
CREATE INDEX IF NOT EXISTS idx_transaction_e_pos_time ON transaction_e_pos(transaction_time);
CREATE INDEX IF NOT EXISTS idx_transaction_e_pos_amount ON transaction_e_pos(transaction_amount);
CREATE INDEX IF NOT EXISTS idx_transaction_e_pos_status ON transaction_e_pos(transaction_trade_status);
CREATE INDEX IF NOT EXISTS idx_transaction_e_pos_channel ON transaction_e_pos(transaction_channel_type);
CREATE INDEX IF NOT EXISTS idx_transaction_e_pos_file_name ON transaction_e_pos(transaction_file_name);

-- Create composite indexes for common filter combinations
CREATE INDEX IF NOT EXISTS idx_transaction_e_pos_time_merchant ON transaction_e_pos(transaction_time, transaction_merchant_id);
CREATE INDEX IF NOT EXISTS idx_transaction_e_pos_time_status ON transaction_e_pos(transaction_time, transaction_trade_status);
CREATE INDEX IF NOT EXISTS idx_transaction_e_pos_merchant_amount ON transaction_e_pos(transaction_merchant_id, transaction_amount);

-- Add constraints
ALTER TABLE transaction_e_pos 
ADD CONSTRAINT chk_transaction_amount_valid 
CHECK (transaction_amount IS NOT NULL);

ALTER TABLE transaction_e_pos 
ADD CONSTRAINT chk_transaction_time_valid 
CHECK (transaction_time IS NOT NULL);

-- Add trigger to automatically update update_dt on record changes
CREATE OR REPLACE FUNCTION update_transaction_e_pos_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    NEW.update_dt = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_transaction_e_pos_timestamp
    BEFORE UPDATE ON transaction_e_pos
    FOR EACH ROW
    EXECUTE FUNCTION update_transaction_e_pos_timestamp();

-- Add comments for documentation
COMMENT ON TABLE transaction_e_pos IS 'Main transaction table for e-commerce payment processing';
COMMENT ON COLUMN transaction_e_pos.id IS 'Primary key - auto-incrementing transaction ID';
COMMENT ON COLUMN transaction_e_pos.reference_no IS 'External reference number from payment system';
COMMENT ON COLUMN transaction_e_pos.transaction_id IS 'Unique transaction identifier';
COMMENT ON COLUMN transaction_e_pos.transaction_out_id IS 'External transaction output ID';
COMMENT ON COLUMN transaction_e_pos.transaction_card_no IS 'Masked card number or payment identifier';
COMMENT ON COLUMN transaction_e_pos.transaction_merchant_id IS 'Merchant ID from merchant table';
COMMENT ON COLUMN transaction_e_pos.transaction_merchant_name IS 'Merchant business name';
COMMENT ON COLUMN transaction_e_pos.transaction_merchant_vat IS 'Merchant VAT/Tax ID';
COMMENT ON COLUMN transaction_e_pos.transaction_time IS 'Transaction timestamp';
COMMENT ON COLUMN transaction_e_pos.transaction_amount IS 'Transaction amount (negative for refunds)';
COMMENT ON COLUMN transaction_e_pos.transaction_refund_id IS 'Refund transaction ID if applicable';
COMMENT ON COLUMN transaction_e_pos.transaction_refund_out_id IS 'External refund transaction ID';
COMMENT ON COLUMN transaction_e_pos.transaction_mch_id IS 'Merchant channel ID';
COMMENT ON COLUMN transaction_e_pos.transaction_sub_mch_id IS 'Sub-merchant/Terminal ID (TID)';
COMMENT ON COLUMN transaction_e_pos.transaction_trade_type IS 'Type of trade (e.g., micropay, native)';
COMMENT ON COLUMN transaction_e_pos.transaction_trade_status IS 'Transaction status (success, refund, failed)';
COMMENT ON COLUMN transaction_e_pos.transaction_bank_type IS 'Bank or card type (e.g., CMB_CREDIT)';
COMMENT ON COLUMN transaction_e_pos.transaction_fee_type IS 'Currency type (e.g., THB, USD)';
COMMENT ON COLUMN transaction_e_pos.transaction_coupon_amount IS 'Coupon or discount amount applied';
COMMENT ON COLUMN transaction_e_pos.transaction_file_name IS 'Original uploaded file name';
COMMENT ON COLUMN transaction_e_pos.transaction_file_name_backup IS 'Backup file path in pCloud';
COMMENT ON COLUMN transaction_e_pos.transaction_channel_type IS 'Payment channel (WeChat, UNIPAY, etc.)';

-- Grant permissions (adjust as needed for your user roles)
-- GRANT SELECT, INSERT, UPDATE, DELETE ON transaction_e_pos TO your_app_user;
-- GRANT USAGE, SELECT ON SEQUENCE transaction_e_pos_id_seq TO your_app_user;

-- Sample data for testing (optional - remove in production)
/*
INSERT INTO transaction_e_pos (
    reference_no, transaction_id, transaction_out_id, transaction_card_no,
    transaction_merchant_id, transaction_merchant_name, transaction_merchant_vat,
    transaction_time, transaction_amount, transaction_sub_mch_id,
    transaction_trade_type, transaction_trade_status, transaction_bank_type,
    transaction_fee_type, transaction_file_name, transaction_channel_type
) VALUES (
    'REF001', 'TXN001', 'OUT001', '****1234',
    'MERCH001', 'Test Merchant', '**********',
    CURRENT_TIMESTAMP, 100.00, 'TID001',
    'micropay', 'success', 'CMB_CREDIT',
    'THB', 'test_transactions.xlsx', 'WeChat'
);
*/

-- Verification queries
-- SELECT COUNT(*) FROM transaction_e_pos;
-- SELECT * FROM transaction_e_pos LIMIT 5;
-- \d transaction_e_pos
