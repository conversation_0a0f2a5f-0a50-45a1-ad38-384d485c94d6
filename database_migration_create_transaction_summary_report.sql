-- Migration: Create transaction_summary_report table for automatic logging of transaction summary reports
-- Date: 2025-07-19
-- Description: Creates tables to store transaction summary report data automatically generated during file processing

-- Create main transaction summary report table
CREATE TABLE IF NOT EXISTS transaction_summary_report (
    id BIGSERIAL PRIMARY KEY,
    
    -- Report metadata
    report_date DATE NOT NULL,
    report_time TIME NOT NULL,
    running_number VARCHAR(50),
    report_type VARCHAR(50) DEFAULT 'PART_DELIVERY_SUMMARY',
    
    -- Processing metadata
    batch_id VARCHAR(100) UNIQUE NOT NULL, -- Unique identifier for this processing batch
    processed_files TEXT[], -- Array of file names processed in this batch
    total_files INTEGER DEFAULT 0,
    total_transactions INTEGER DEFAULT 0,
    
    -- Grand totals from the report
    grand_total_transactions INTEGER DEFAULT 0,
    grand_total_amount DECIMAL(18,2) DEFAULT 0,
    grand_total_mdr_amount DECIMAL(18,2) DEFAULT 0,
    grand_total_vat_amount DECIMAL(18,2) DEFAULT 0,
    grand_total_net_amount DECIMAL(18,2) DEFAULT 0,
    grand_total_withhold_tax DECIMAL(18,2) DEFAULT 0,
    grand_total_transfer_fee DECIMAL(18,2) DEFAULT 0,
    grand_total_reimbursement_fee DECIMAL(18,2) DEFAULT 0,
    grand_total_service_fee DECIMAL(18,2) DEFAULT 0,
    grand_total_final_net_amount DECIMAL(18,2) DEFAULT 0,
    grand_total_cup_business_tax_fee DECIMAL(18,2) DEFAULT 0,
    
    -- Average rates
    average_mdr_rate DECIMAL(8,4) DEFAULT 0,
    average_vat_percentage DECIMAL(8,4) DEFAULT 0,
    average_withholding_tax_rate DECIMAL(8,4) DEFAULT 0,
    
    -- Status and processing info
    status VARCHAR(20) DEFAULT 'GENERATED', -- GENERATED, APPROVED, CANCELLED
    approved_by VARCHAR(100),
    approved_at TIMESTAMP,
    
    -- Standard audit columns
    create_by VARCHAR(100) DEFAULT 'SYSTEM',
    create_dt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_by VARCHAR(100) DEFAULT 'SYSTEM',
    update_dt TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create transaction summary report details table (merchant-level data)
CREATE TABLE IF NOT EXISTS transaction_summary_report_detail (
    id BIGSERIAL PRIMARY KEY,
    report_id BIGINT NOT NULL REFERENCES transaction_summary_report(id) ON DELETE CASCADE,
    
    -- Merchant information
    merchant_vat VARCHAR(100),
    merchant_name VARCHAR(255),
    transaction_date DATE,
    channel_type VARCHAR(50),
    
    -- Transaction counts and amounts
    transaction_count INTEGER DEFAULT 0,
    total_amount DECIMAL(18,2) DEFAULT 0,
    
    -- Financial calculations (matching PDF report structure)
    mdr_rate DECIMAL(8,4) DEFAULT 0, -- From merchant_wechat.wechat_rate
    mdr_amount DECIMAL(18,2) DEFAULT 0,
    vat_percentage DECIMAL(8,4) DEFAULT 0, -- From network_service.vat_tax_percent
    vat_amount DECIMAL(18,2) DEFAULT 0,
    net_amount DECIMAL(18,2) DEFAULT 0, -- Total Amount - MDR Amount
    withholding_tax_rate DECIMAL(8,4) DEFAULT 0, -- From merchant.withholding_tax
    withhold_tax DECIMAL(18,2) DEFAULT 0,
    transfer_fee DECIMAL(18,2) DEFAULT 0, -- From merchant.transfer_fee
    reimbursement_fee DECIMAL(18,2) DEFAULT 0, -- Transaction Amount × 0.5%
    service_fee DECIMAL(18,2) DEFAULT 0,
    final_net_amount DECIMAL(18,2) DEFAULT 0, -- Net Amount - (Transfer Fee + Reimbursement Fee + Service Fee + CUP Business Tax)
    cup_business_tax_fee DECIMAL(18,2) DEFAULT 0,

    -- Transfer status tracking
    is_transfer SMALLINT DEFAULT 0 CHECK (is_transfer IN (0, 1)), -- 0 = not yet transferred, 1 = transfer already

    -- Standard audit columns
    create_by VARCHAR(100) DEFAULT 'SYSTEM',
    create_dt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_by VARCHAR(100) DEFAULT 'SYSTEM',
    update_dt TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for performance optimization
CREATE INDEX IF NOT EXISTS idx_transaction_summary_report_date ON transaction_summary_report(report_date);
CREATE INDEX IF NOT EXISTS idx_transaction_summary_report_batch_id ON transaction_summary_report(batch_id);
CREATE INDEX IF NOT EXISTS idx_transaction_summary_report_status ON transaction_summary_report(status);
CREATE INDEX IF NOT EXISTS idx_transaction_summary_report_create_dt ON transaction_summary_report(create_dt);

CREATE INDEX IF NOT EXISTS idx_transaction_summary_report_detail_report_id ON transaction_summary_report_detail(report_id);
CREATE INDEX IF NOT EXISTS idx_transaction_summary_report_detail_merchant_vat ON transaction_summary_report_detail(merchant_vat);
CREATE INDEX IF NOT EXISTS idx_transaction_summary_report_detail_date ON transaction_summary_report_detail(transaction_date);
CREATE INDEX IF NOT EXISTS idx_transaction_summary_report_detail_channel ON transaction_summary_report_detail(channel_type);
CREATE INDEX IF NOT EXISTS idx_transaction_summary_report_detail_transfer ON transaction_summary_report_detail(is_transfer);

-- Create composite indexes for common queries
CREATE INDEX IF NOT EXISTS idx_transaction_summary_report_date_status ON transaction_summary_report(report_date, status);
CREATE INDEX IF NOT EXISTS idx_transaction_summary_report_detail_report_merchant ON transaction_summary_report_detail(report_id, merchant_vat);

-- Add trigger to automatically update update_dt on record changes
CREATE OR REPLACE FUNCTION update_transaction_summary_report_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    NEW.update_dt = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_transaction_summary_report_timestamp
    BEFORE UPDATE ON transaction_summary_report
    FOR EACH ROW
    EXECUTE FUNCTION update_transaction_summary_report_timestamp();

CREATE TRIGGER trigger_update_transaction_summary_report_detail_timestamp
    BEFORE UPDATE ON transaction_summary_report_detail
    FOR EACH ROW
    EXECUTE FUNCTION update_transaction_summary_report_timestamp();

-- Add constraints
ALTER TABLE transaction_summary_report 
ADD CONSTRAINT chk_report_date_valid 
CHECK (report_date IS NOT NULL);

ALTER TABLE transaction_summary_report 
ADD CONSTRAINT chk_batch_id_valid 
CHECK (batch_id IS NOT NULL AND batch_id != '');

ALTER TABLE transaction_summary_report_detail 
ADD CONSTRAINT chk_transaction_count_valid 
CHECK (transaction_count >= 0);

ALTER TABLE transaction_summary_report_detail 
ADD CONSTRAINT chk_total_amount_valid 
CHECK (total_amount >= 0);

-- Add comments for documentation
COMMENT ON TABLE transaction_summary_report IS 'Main table for storing transaction summary reports generated during file processing';
COMMENT ON TABLE transaction_summary_report_detail IS 'Detail table storing merchant-level data for each transaction summary report';

COMMENT ON COLUMN transaction_summary_report.batch_id IS 'Unique identifier linking this report to a specific file processing batch';
COMMENT ON COLUMN transaction_summary_report.processed_files IS 'Array of file names that were processed to generate this report';
COMMENT ON COLUMN transaction_summary_report.status IS 'Report status: GENERATED, APPROVED, CANCELLED';

COMMENT ON COLUMN transaction_summary_report_detail.mdr_rate IS 'Merchant Discount Rate percentage from merchant_wechat.wechat_rate';
COMMENT ON COLUMN transaction_summary_report_detail.vat_percentage IS 'VAT percentage from network_service.vat_tax_percent';
COMMENT ON COLUMN transaction_summary_report_detail.withholding_tax_rate IS 'Withholding tax rate from merchant.withholding_tax';
COMMENT ON COLUMN transaction_summary_report_detail.final_net_amount IS 'Final amount after all deductions and fees';
COMMENT ON COLUMN transaction_summary_report_detail.is_transfer IS 'Transfer status: 0 = not yet transferred, 1 = transfer already';

-- Grant permissions (adjust as needed for your user roles)
-- GRANT SELECT, INSERT, UPDATE, DELETE ON transaction_summary_report TO your_app_user;
-- GRANT SELECT, INSERT, UPDATE, DELETE ON transaction_summary_report_detail TO your_app_user;
-- GRANT USAGE, SELECT ON SEQUENCE transaction_summary_report_id_seq TO your_app_user;
-- GRANT USAGE, SELECT ON SEQUENCE transaction_summary_report_detail_id_seq TO your_app_user;
