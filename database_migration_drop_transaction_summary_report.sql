-- Migration: Drop transaction_summary_report table and keep only transaction_summary_report_detail
-- Date: 2025-07-23
-- Description: Removes the main transaction_summary_report table since we only use transaction_summary_report_detail

-- Step 1: First, let's check what dependencies exist
SELECT
    'Checking dependencies before migration' as status,
    conname as constraint_name,
    conrelid::regclass as table_name,
    confrelid::regclass as referenced_table
FROM pg_constraint
WHERE confrelid = 'transaction_summary_report'::regclass;

-- Step 2: Drop dependent views that reference the main table
DROP VIEW IF EXISTS v_transaction_summary_workflow CASCADE;

-- Step 3: Drop functions that reference the main table
DROP FUNCTION IF EXISTS get_merchant_summary_stats(DATE, DATE) CASCADE;

-- Step 4: Remove foreign key constraint from detail table explicitly
ALTER TABLE transaction_summary_report_detail
DROP CONSTRAINT IF EXISTS transaction_summary_report_detail_report_id_fkey CASCADE;

-- Step 5: Drop any other constraints that might reference the main table
DO $$
DECLARE
    constraint_record RECORD;
BEGIN
    -- Find and drop all foreign key constraints that reference transaction_summary_report
    FOR constraint_record IN
        SELECT conname, conrelid::regclass as table_name
        FROM pg_constraint
        WHERE confrelid = 'transaction_summary_report'::regclass
    LOOP
        EXECUTE format('ALTER TABLE %s DROP CONSTRAINT IF EXISTS %s CASCADE',
                      constraint_record.table_name, constraint_record.conname);
        RAISE NOTICE 'Dropped constraint % from table %', constraint_record.conname, constraint_record.table_name;
    END LOOP;
END $$;

-- Step 6: Drop triggers on the main table
DROP TRIGGER IF EXISTS trigger_update_transaction_summary_report_timestamp ON transaction_summary_report CASCADE;

-- Step 7: Drop the main transaction_summary_report table with CASCADE to handle any remaining dependencies
DROP TABLE IF EXISTS transaction_summary_report CASCADE;

-- Step 6: Drop the shared trigger function if no longer needed
-- (Keep it commented out in case other tables use it)
-- DROP FUNCTION IF EXISTS update_transaction_summary_report_timestamp();

-- Step 8: Remove the report_id column since it's no longer needed
ALTER TABLE transaction_summary_report_detail
DROP COLUMN IF EXISTS report_id CASCADE;

-- Step 9: Add any missing columns to detail table that might be needed
-- (Add report metadata columns if they don't exist)
ALTER TABLE transaction_summary_report_detail
ADD COLUMN IF NOT EXISTS batch_id VARCHAR(100);

ALTER TABLE transaction_summary_report_detail
ADD COLUMN IF NOT EXISTS report_date DATE;

ALTER TABLE transaction_summary_report_detail
ADD COLUMN IF NOT EXISTS report_time TIME;

ALTER TABLE transaction_summary_report_detail
ADD COLUMN IF NOT EXISTS running_number VARCHAR(50);

ALTER TABLE transaction_summary_report_detail
ADD COLUMN IF NOT EXISTS processed_files TEXT[];

-- Add status column to track report status at detail level if needed
ALTER TABLE transaction_summary_report_detail
ADD COLUMN IF NOT EXISTS status VARCHAR(50) DEFAULT 'GENERATED';

-- Add total files and transactions columns for batch tracking
ALTER TABLE transaction_summary_report_detail
ADD COLUMN IF NOT EXISTS total_files INTEGER DEFAULT 0;

ALTER TABLE transaction_summary_report_detail
ADD COLUMN IF NOT EXISTS total_transactions INTEGER DEFAULT 0;

-- Step 10: Drop old indexes that referenced report_id
DROP INDEX IF EXISTS idx_transaction_summary_report_detail_report_id CASCADE;
DROP INDEX IF EXISTS idx_transaction_summary_report_detail_report_merchant CASCADE;

-- Step 11: Create indexes for the new columns
CREATE INDEX IF NOT EXISTS idx_transaction_summary_report_detail_batch_id
ON transaction_summary_report_detail(batch_id);

CREATE INDEX IF NOT EXISTS idx_transaction_summary_report_detail_report_date
ON transaction_summary_report_detail(report_date);

CREATE INDEX IF NOT EXISTS idx_transaction_summary_report_detail_status
ON transaction_summary_report_detail(status);

-- Create composite index for common queries
CREATE INDEX IF NOT EXISTS idx_transaction_summary_report_detail_batch_merchant
ON transaction_summary_report_detail(batch_id, merchant_vat);

-- Step 12: Update the trigger to work with detail table only (if not already exists)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_trigger
        WHERE tgname = 'trigger_update_transaction_summary_report_detail_timestamp'
        AND tgrelid = 'transaction_summary_report_detail'::regclass
    ) THEN
        CREATE TRIGGER trigger_update_transaction_summary_report_detail_timestamp
            BEFORE UPDATE ON transaction_summary_report_detail
            FOR EACH ROW
            EXECUTE FUNCTION update_transaction_summary_report_timestamp();
    END IF;
END $$;

-- Step 13: Create a simplified view for the detail table
CREATE OR REPLACE VIEW v_transaction_summary_detail_workflow AS
SELECT
    batch_id,
    report_date,
    running_number,
    status,
    COUNT(*) as merchant_summary_count,
    SUM(CASE WHEN is_transfer = 1 THEN 1 ELSE 0 END) as transferred_count,
    SUM(CASE WHEN is_transfer = 0 THEN 1 ELSE 0 END) as pending_count,
    SUM(final_net_amount) as total_final_net_amount,
    SUM(total_amount) as grand_total_amount,
    MIN(create_dt) as first_created,
    MAX(update_dt) as last_updated,
    MAX(total_files) as total_files,
    MAX(total_transactions) as total_transactions
FROM transaction_summary_report_detail
WHERE batch_id IS NOT NULL
GROUP BY batch_id, report_date, running_number, status
ORDER BY report_date DESC, first_created DESC;

-- Step 14: Add comments for documentation
COMMENT ON TABLE transaction_summary_report_detail IS 'Detail table storing merchant-level transaction summary data (main table removed)';
COMMENT ON COLUMN transaction_summary_report_detail.batch_id IS 'Unique identifier for the processing batch (moved from main table)';
COMMENT ON COLUMN transaction_summary_report_detail.report_date IS 'Date of the report (moved from main table)';
COMMENT ON COLUMN transaction_summary_report_detail.status IS 'Report status: GENERATED, APPROVED, CANCELLED (moved from main table)';

-- Step 15: Verification queries
SELECT 'Migration completed successfully - transaction_summary_report table dropped' as status;

-- Verify the main table is gone
SELECT
    CASE
        WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'transaction_summary_report')
        THEN 'ERROR: transaction_summary_report table still exists!'
        ELSE 'SUCCESS: transaction_summary_report table has been dropped'
    END as table_drop_status;

-- Show the updated table structure
SELECT
    'Updated transaction_summary_report_detail structure:' as info,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns
WHERE table_name = 'transaction_summary_report_detail'
ORDER BY ordinal_position;

-- Show new indexes
SELECT
    'New indexes created:' as info,
    indexname,
    indexdef
FROM pg_indexes
WHERE tablename = 'transaction_summary_report_detail'
AND indexname LIKE '%batch%' OR indexname LIKE '%report_date%' OR indexname LIKE '%status%';

-- Show the new view
SELECT 'New view created: v_transaction_summary_detail_workflow' as view_status;
