-- Migration: Enhanced Workflow for Transaction Summary Report Generation
-- Date: 2025-07-19
-- Description: Ensures database is ready for enhanced workflow that groups transactions by merchant_id

-- Verify that required tables exist
DO $$
BEGIN
    -- Check if transaction_e_pos table exists
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'transaction_e_pos') THEN
        RAISE EXCEPTION 'transaction_e_pos table does not exist. Please run the main transaction table migration first.';
    END IF;

    -- Check if transaction_summary_report table exists
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'transaction_summary_report') THEN
        RAISE EXCEPTION 'transaction_summary_report table does not exist. Please run the summary report migration first.';
    END IF;

    -- Check if transaction_summary_report_detail table exists
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'transaction_summary_report_detail') THEN
        RAISE EXCEPTION 'transaction_summary_report_detail table does not exist. Please run the summary report migration first.';
    END IF;

    RAISE NOTICE 'All required tables exist. Enhanced workflow is ready.';
END $$;

-- Ensure is_transfer field exists in transaction_summary_report_detail
ALTER TABLE transaction_summary_report_detail
ADD COLUMN IF NOT EXISTS is_transfer SMALLINT DEFAULT 0;

-- Add check constraint if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.check_constraints
        WHERE constraint_name = 'chk_is_transfer_valid'
    ) THEN
        ALTER TABLE transaction_summary_report_detail
        ADD CONSTRAINT chk_is_transfer_valid
        CHECK (is_transfer IN (0, 1));
    END IF;
END $$;

-- Ensure index exists for is_transfer
CREATE INDEX IF NOT EXISTS idx_transaction_summary_report_detail_transfer
ON transaction_summary_report_detail(is_transfer);

-- Add comment for the field
COMMENT ON COLUMN transaction_summary_report_detail.is_transfer IS 'Transfer status: 0 = not yet transferred, 1 = transfer already';

-- Update existing records to have default transfer status
UPDATE transaction_summary_report_detail
SET is_transfer = 0
WHERE is_transfer IS NULL;

-- Make the column NOT NULL after setting default values
ALTER TABLE transaction_summary_report_detail
ALTER COLUMN is_transfer SET NOT NULL;

-- Create indexes for enhanced workflow performance
CREATE INDEX IF NOT EXISTS idx_transaction_e_pos_merchant_id_file 
ON transaction_e_pos(transaction_merchant_id, transaction_file_name);

CREATE INDEX IF NOT EXISTS idx_transaction_e_pos_merchant_vat_file 
ON transaction_e_pos(transaction_merchant_vat, transaction_file_name);

CREATE INDEX IF NOT EXISTS idx_transaction_e_pos_status_file 
ON transaction_e_pos(transaction_trade_status, transaction_file_name);

CREATE INDEX IF NOT EXISTS idx_transaction_e_pos_time_merchant 
ON transaction_e_pos(transaction_time, transaction_merchant_id);

-- Create composite index for enhanced workflow queries
CREATE INDEX IF NOT EXISTS idx_transaction_e_pos_enhanced_workflow 
ON transaction_e_pos(transaction_file_name, transaction_trade_status, transaction_merchant_id);

-- Add indexes for merchant-related tables if they exist
DO $$
BEGIN
    -- Index for merchant table if it exists
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'merchant') THEN
        CREATE INDEX IF NOT EXISTS idx_merchant_vat ON merchant(merchant_vat);
        CREATE INDEX IF NOT EXISTS idx_merchant_id ON merchant(merchant_id);
    END IF;

    -- Index for merchant_wechat table if it exists
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'merchant_wechat') THEN
        CREATE INDEX IF NOT EXISTS idx_merchant_wechat_merchant_id ON merchant_wechat(merchant_id);
    END IF;

    -- Index for network_service table if it exists
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'network_service') THEN
        CREATE INDEX IF NOT EXISTS idx_network_service_active ON network_service(active);
    END IF;
END $$;

-- Create a view for enhanced workflow monitoring
CREATE OR REPLACE VIEW v_transaction_summary_workflow AS
SELECT 
    r.id as report_id,
    r.batch_id,
    r.report_date,
    r.running_number,
    r.total_files,
    r.total_transactions,
    r.grand_total_amount,
    r.status as report_status,
    r.create_by,
    r.create_dt,
    COUNT(d.id) as merchant_summary_count,
    SUM(CASE WHEN d.is_transfer = 1 THEN 1 ELSE 0 END) as transferred_count,
    SUM(CASE WHEN d.is_transfer = 0 THEN 1 ELSE 0 END) as pending_count,
    SUM(d.final_net_amount) as total_final_net_amount
FROM transaction_summary_report r
LEFT JOIN transaction_summary_report_detail d ON r.id = d.report_id
GROUP BY r.id, r.batch_id, r.report_date, r.running_number, r.total_files, 
         r.total_transactions, r.grand_total_amount, r.status, r.create_by, r.create_dt
ORDER BY r.create_dt DESC;

-- Add comment for the view
COMMENT ON VIEW v_transaction_summary_workflow IS 'View for monitoring enhanced workflow transaction summary reports with transfer status';

-- Create a function to get merchant summary statistics
CREATE OR REPLACE FUNCTION get_merchant_summary_stats(
    p_start_date DATE DEFAULT NULL,
    p_end_date DATE DEFAULT NULL
)
RETURNS TABLE (
    merchant_vat VARCHAR,
    merchant_name VARCHAR,
    total_reports BIGINT,
    total_transactions BIGINT,
    total_amount NUMERIC,
    transferred_reports BIGINT,
    pending_reports BIGINT,
    last_report_date DATE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        d.merchant_vat,
        d.merchant_name,
        COUNT(DISTINCT d.report_id) as total_reports,
        SUM(d.transaction_count) as total_transactions,
        SUM(d.total_amount) as total_amount,
        SUM(CASE WHEN d.is_transfer = 1 THEN 1 ELSE 0 END) as transferred_reports,
        SUM(CASE WHEN d.is_transfer = 0 THEN 1 ELSE 0 END) as pending_reports,
        MAX(r.report_date) as last_report_date
    FROM transaction_summary_report_detail d
    JOIN transaction_summary_report r ON d.report_id = r.id
    WHERE (p_start_date IS NULL OR r.report_date >= p_start_date)
    AND (p_end_date IS NULL OR r.report_date <= p_end_date)
    GROUP BY d.merchant_vat, d.merchant_name
    ORDER BY total_amount DESC;
END;
$$ LANGUAGE plpgsql;

-- Add comment for the function
COMMENT ON FUNCTION get_merchant_summary_stats IS 'Function to get merchant summary statistics for enhanced workflow monitoring';

-- Verify the enhanced workflow setup
SELECT 
    'Enhanced Workflow Setup Complete' as status,
    COUNT(*) as existing_reports
FROM transaction_summary_report;

-- Show sample data if any exists
SELECT 
    'Sample Enhanced Workflow Data' as info,
    r.batch_id,
    r.report_date,
    COUNT(d.id) as merchant_summaries,
    SUM(d.transaction_count) as total_transactions,
    SUM(d.total_amount) as total_amount
FROM transaction_summary_report r
LEFT JOIN transaction_summary_report_detail d ON r.id = d.report_id
GROUP BY r.id, r.batch_id, r.report_date
ORDER BY r.create_dt DESC
LIMIT 5;

-- Performance check query
EXPLAIN (ANALYZE, BUFFERS) 
SELECT 
    t.transaction_merchant_id,
    COUNT(*) as transaction_count,
    SUM(t.transaction_amount::NUMERIC) as total_amount
FROM transaction_e_pos t
WHERE t.transaction_file_name = 'sample_file.csv'
AND t.transaction_trade_status = 'success'
GROUP BY t.transaction_merchant_id
LIMIT 1;

-- Final status messages
DO $$
BEGIN
    RAISE NOTICE 'Enhanced workflow migration completed successfully!';
    RAISE NOTICE 'The system is now ready to automatically generate merchant-grouped summary reports.';
    RAISE NOTICE 'Upload transaction files through the Transaction Management screen to test the enhanced workflow.';
END $$;
