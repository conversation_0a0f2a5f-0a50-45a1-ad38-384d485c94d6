-- Migration: Make bank_ref field required in tmst_bank table
-- Date: 2025-07-23
-- Description: Updates bank_ref field to be NOT NULL and adds default values for existing records

-- First, update any existing NULL bank_ref values with a default value
UPDATE tmst_bank 
SET bank_ref = CONCAT('REF_', bank_code)
WHERE bank_ref IS NULL OR bank_ref = '';

-- Now make the column NOT NULL
ALTER TABLE tmst_bank 
ALTER COLUMN bank_ref SET NOT NULL;

-- Add a check constraint to ensure bank_ref is not empty
ALTER TABLE tmst_bank 
ADD CONSTRAINT chk_bank_ref_not_empty 
CHECK (bank_ref IS NOT NULL AND LENGTH(TRIM(bank_ref)) > 0);

-- Add comment for documentation
COMMENT ON COLUMN tmst_bank.bank_ref IS 'Bank reference code - required field, maximum 20 characters';

-- Verify the changes
SELECT 
    column_name, 
    data_type, 
    is_nullable, 
    column_default,
    character_maximum_length
FROM information_schema.columns 
WHERE table_name = 'tmst_bank' 
AND column_name = 'bank_ref';

-- Show any constraints on bank_ref
SELECT 
    constraint_name, 
    constraint_type, 
    check_clause
FROM information_schema.check_constraints 
WHERE constraint_name LIKE '%bank_ref%';

-- Show sample data to verify
SELECT bank_id, bank_code, bank_ref, bank_name_en 
FROM tmst_bank 
LIMIT 5;
