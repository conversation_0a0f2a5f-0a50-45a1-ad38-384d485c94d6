-- Migration: Remove specific columns from transaction_summary_report_detail table
-- Date: 2025-07-23
-- Description: Removes batch_id, report_date, report_time, running_number, processed_files, status, total_files, total_transactions columns

-- Step 1: Check current table structure before migration
SELECT 
    'Current transaction_summary_report_detail structure before migration:' as info,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns
WHERE table_name = 'transaction_summary_report_detail'
ORDER BY ordinal_position;

-- Step 2: Drop any views that depend on these columns
DROP VIEW IF EXISTS v_transaction_summary_detail_workflow CASCADE;

-- Step 3: Drop any indexes that use these columns
DROP INDEX IF EXISTS idx_transaction_summary_report_detail_batch_id CASCADE;
DROP INDEX IF EXISTS idx_transaction_summary_report_detail_report_date CASCADE;
DROP INDEX IF EXISTS idx_transaction_summary_report_detail_status CASCADE;
DROP INDEX IF EXISTS idx_transaction_summary_report_detail_batch_date CASCADE;

-- Step 4: Remove the specific columns from transaction_summary_report_detail table
ALTER TABLE transaction_summary_report_detail
DROP COLUMN IF EXISTS batch_id CASCADE;

ALTER TABLE transaction_summary_report_detail
DROP COLUMN IF EXISTS report_date CASCADE;

ALTER TABLE transaction_summary_report_detail
DROP COLUMN IF EXISTS report_time CASCADE;

ALTER TABLE transaction_summary_report_detail
DROP COLUMN IF EXISTS running_number CASCADE;

ALTER TABLE transaction_summary_report_detail
DROP COLUMN IF EXISTS processed_files CASCADE;

ALTER TABLE transaction_summary_report_detail
DROP COLUMN IF EXISTS status CASCADE;

ALTER TABLE transaction_summary_report_detail
DROP COLUMN IF EXISTS total_files CASCADE;

ALTER TABLE transaction_summary_report_detail
DROP COLUMN IF EXISTS total_transactions CASCADE;

-- Step 5: Create new indexes for remaining important columns
CREATE INDEX IF NOT EXISTS idx_transaction_summary_report_detail_merchant_vat 
ON transaction_summary_report_detail(merchant_vat);

CREATE INDEX IF NOT EXISTS idx_transaction_summary_report_detail_transaction_date 
ON transaction_summary_report_detail(transaction_date);

CREATE INDEX IF NOT EXISTS idx_transaction_summary_report_detail_channel_type 
ON transaction_summary_report_detail(channel_type);

CREATE INDEX IF NOT EXISTS idx_transaction_summary_report_detail_is_transfer 
ON transaction_summary_report_detail(is_transfer);

CREATE INDEX IF NOT EXISTS idx_transaction_summary_report_detail_create_dt 
ON transaction_summary_report_detail(create_dt);

-- Step 6: Create a simplified view for the detail table (without removed columns)
CREATE OR REPLACE VIEW v_transaction_summary_detail_simple AS
SELECT
    merchant_vat,
    merchant_name,
    transaction_date,
    channel_type,
    COUNT(*) as record_count,
    SUM(transaction_count) as total_transactions,
    SUM(total_amount) as total_amount,
    SUM(final_net_amount) as total_final_net_amount,
    SUM(CASE WHEN is_transfer = 1 THEN 1 ELSE 0 END) as transferred_count,
    SUM(CASE WHEN is_transfer = 0 THEN 1 ELSE 0 END) as pending_count,
    MIN(create_dt) as first_created,
    MAX(update_dt) as last_updated
FROM transaction_summary_report_detail
GROUP BY merchant_vat, merchant_name, transaction_date, channel_type
ORDER BY transaction_date DESC, merchant_vat;

-- Step 7: Update table comments
COMMENT ON TABLE transaction_summary_report_detail IS 'Detail table storing merchant-level transaction summary data (simplified structure)';
COMMENT ON COLUMN transaction_summary_report_detail.merchant_vat IS 'Merchant VAT number';
COMMENT ON COLUMN transaction_summary_report_detail.merchant_name IS 'Merchant name';
COMMENT ON COLUMN transaction_summary_report_detail.transaction_date IS 'Date of the transactions';
COMMENT ON COLUMN transaction_summary_report_detail.channel_type IS 'Payment channel type (e.g., WeChat, UNIPAY)';
COMMENT ON COLUMN transaction_summary_report_detail.is_transfer IS 'Transfer status: 0 = not yet transferred, 1 = transfer completed';

-- Step 8: Verification queries
SELECT 'Migration completed successfully - columns removed from transaction_summary_report_detail' as status;

-- Show the updated table structure
SELECT
    'Updated transaction_summary_report_detail structure after migration:' as info,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns
WHERE table_name = 'transaction_summary_report_detail'
ORDER BY ordinal_position;

-- Show new indexes
SELECT
    'New indexes created:' as info,
    indexname,
    indexdef
FROM pg_indexes
WHERE tablename = 'transaction_summary_report_detail'
ORDER BY indexname;

-- Show the new simplified view
SELECT 'New view created: v_transaction_summary_detail_simple' as view_status;

-- Show sample data structure
SELECT 
    'Sample data structure after migration:' as info,
    COUNT(*) as total_records,
    COUNT(DISTINCT merchant_vat) as unique_merchants,
    MIN(transaction_date) as earliest_date,
    MAX(transaction_date) as latest_date
FROM transaction_summary_report_detail;

-- Final verification - ensure removed columns are gone
SELECT 
    CASE 
        WHEN COUNT(*) = 0 THEN 'SUCCESS: All specified columns have been removed'
        ELSE 'WARNING: Some columns still exist: ' || string_agg(column_name, ', ')
    END as verification_result
FROM information_schema.columns
WHERE table_name = 'transaction_summary_report_detail'
AND column_name IN ('batch_id', 'report_date', 'report_time', 'running_number', 'processed_files', 'status', 'total_files', 'total_transactions');
