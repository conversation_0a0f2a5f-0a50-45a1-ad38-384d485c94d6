-- Migration: Rename is_transfer_fee to is_transfer in transaction_summary_report_detail table
-- Date: 2025-07-19
-- Description: Renames the transfer status field for better clarity and consistency

-- Check if the old field exists and new field doesn't exist
DO $$
BEGIN
    -- Check if we need to rename the field
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'transaction_summary_report_detail' 
        AND column_name = 'is_transfer_fee'
    ) AND NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'transaction_summary_report_detail' 
        AND column_name = 'is_transfer'
    ) THEN
        -- Rename the column
        ALTER TABLE transaction_summary_report_detail 
        RENAME COLUMN is_transfer_fee TO is_transfer;
        
        RAISE NOTICE 'Column is_transfer_fee renamed to is_transfer';
    ELSIF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'transaction_summary_report_detail' 
        AND column_name = 'is_transfer'
    ) THEN
        RAISE NOTICE 'Column is_transfer already exists, no rename needed';
    ELSE
        RAISE NOTICE 'Neither is_transfer_fee nor is_transfer exists, creating is_transfer';
        -- Add the new column if neither exists
        ALTER TABLE transaction_summary_report_detail 
        ADD COLUMN is_transfer SMALLINT DEFAULT 0 CHECK (is_transfer IN (0, 1));
    END IF;
END $$;

-- Drop old constraint if it exists and create new one
DO $$
BEGIN
    -- Drop old constraint if it exists
    IF EXISTS (
        SELECT 1 FROM information_schema.check_constraints 
        WHERE constraint_name = 'chk_is_transfer_fee_valid'
    ) THEN
        ALTER TABLE transaction_summary_report_detail 
        DROP CONSTRAINT chk_is_transfer_fee_valid;
        RAISE NOTICE 'Dropped old constraint chk_is_transfer_fee_valid';
    END IF;
    
    -- Add new constraint if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.check_constraints 
        WHERE constraint_name = 'chk_is_transfer_valid'
    ) THEN
        ALTER TABLE transaction_summary_report_detail 
        ADD CONSTRAINT chk_is_transfer_valid 
        CHECK (is_transfer IN (0, 1));
        RAISE NOTICE 'Added new constraint chk_is_transfer_valid';
    END IF;
END $$;

-- Drop old index if it exists and create new one
DROP INDEX IF EXISTS idx_transaction_summary_report_detail_transfer_fee;
CREATE INDEX IF NOT EXISTS idx_transaction_summary_report_detail_transfer 
ON transaction_summary_report_detail(is_transfer);

-- Update comment
COMMENT ON COLUMN transaction_summary_report_detail.is_transfer IS 'Transfer status: 0 = not yet transferred, 1 = transfer already';

-- Ensure the column is NOT NULL with default value
DO $$
BEGIN
    -- Update any NULL values to 0
    UPDATE transaction_summary_report_detail 
    SET is_transfer = 0 
    WHERE is_transfer IS NULL;
    
    -- Set NOT NULL constraint
    ALTER TABLE transaction_summary_report_detail 
    ALTER COLUMN is_transfer SET NOT NULL;
    
    RAISE NOTICE 'Set is_transfer column to NOT NULL with default values';
END $$;

-- Verify the migration
SELECT 
    'Migration Verification' as status,
    column_name, 
    data_type, 
    is_nullable, 
    column_default
FROM information_schema.columns 
WHERE table_name = 'transaction_summary_report_detail' 
AND column_name = 'is_transfer';

-- Show constraint information
SELECT 
    'Constraint Verification' as status,
    constraint_name,
    check_clause
FROM information_schema.check_constraints 
WHERE constraint_name = 'chk_is_transfer_valid';

-- Show index information
SELECT 
    'Index Verification' as status,
    indexname,
    indexdef
FROM pg_indexes 
WHERE tablename = 'transaction_summary_report_detail' 
AND indexname = 'idx_transaction_summary_report_detail_transfer';

-- Show sample data with the new field
SELECT 
    'Sample Data' as info,
    id, 
    merchant_vat, 
    merchant_name, 
    final_net_amount, 
    is_transfer,
    CASE 
        WHEN is_transfer = 0 THEN 'Not Yet Transferred'
        WHEN is_transfer = 1 THEN 'Transfer Already'
        ELSE 'Unknown Status'
    END as transfer_status_description
FROM transaction_summary_report_detail 
ORDER BY id 
LIMIT 5;

RAISE NOTICE 'Field rename migration completed successfully!';
RAISE NOTICE 'The field is_transfer_fee has been renamed to is_transfer for better clarity.';
RAISE NOTICE 'All constraints, indexes, and comments have been updated accordingly.';
