appId: com.daltonmenezes.electronrouterdom
productName: Eposservice
electronVersion: 21.2.3
directories:
  buildResources: build
files:
  - '!**/.vscode/*'
  - '!src/*'
  - '!electron.vite.config.{js,ts,mjs,cjs}'
  - '!{.eslintignore,.eslintrc.cjs,.prettierignore,.prettierrc.yaml,dev-app-update.yml,CHANGELOG.md,README.md}'
  - '!{tsconfig.json,tsconfig.node.json,tsconfig.web.json}'
extraResources:
  - from: 'src/assets/rounded_corners.png'
    to: 'assets/rounded_corners.png'
  - from: '.env.local'
    to: '.env.local'
  - from: '.env.prod'
    to: '.env.prod'
asarUnpack:
  - '**/*.{node,dll}'
win:
  icon: build/icon.ico
mac:
  category: public.app-category.developer-tools
  icon: build/icon.icns
  target:
    - dir
linux:
  icon: build/icon.png
  target:
    - deb
  maintainer: daltonmenezes.com
  synopsis: ${description}
  category: Utility
npmRebuild: false
