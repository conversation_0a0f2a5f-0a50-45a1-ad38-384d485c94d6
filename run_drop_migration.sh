#!/bin/bash

# <PERSON><PERSON>t to safely run the drop transaction_summary_report table migration
# This script will backup data before dropping the table

echo "🚨 CRITICAL MIGRATION: Dropping transaction_summary_report table"
echo "⚠️  This will permanently delete the main transaction summary report table!"
echo ""

# Database connection string
DB_CONNECTION="postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require"

# Ask for confirmation
read -p "Are you sure you want to proceed? This action cannot be undone! (type 'YES' to continue): " confirmation

if [ "$confirmation" != "YES" ]; then
    echo "❌ Migration cancelled by user"
    exit 1
fi

echo ""
echo "📋 Step 1: Creating backup of existing data..."

# Create backup of main table data
echo "📊 Backing up transaction_summary_report data..."
psql "$DB_CONNECTION" -c "\COPY (SELECT * FROM transaction_summary_report) TO 'backup_transaction_summary_report_$(date +%Y%m%d_%H%M%S).csv' WITH CSV HEADER;"

if [ $? -eq 0 ]; then
    echo "✅ Backup created successfully"
else
    echo "❌ Backup failed - stopping migration"
    exit 1
fi

echo ""
echo "📋 Step 2: Running migration to drop transaction_summary_report table..."
psql "$DB_CONNECTION" -f database_migration_drop_transaction_summary_report.sql

if [ $? -eq 0 ]; then
    echo "✅ Migration completed successfully"
    echo ""
    echo "📊 Summary of changes:"
    echo "   • transaction_summary_report table has been DROPPED"
    echo "   • transaction_summary_report_detail table updated with new columns"
    echo "   • New view v_transaction_summary_detail_workflow created"
    echo "   • Foreign key constraints removed"
    echo "   • Dependent views and functions dropped"
    echo ""
    echo "⚠️  IMPORTANT: You need to update your application code!"
    echo "   • Update transactionHandler.ts IPC methods"
    echo "   • Update transactionSummaryReportService.ts"
    echo "   • Test all transaction summary functionality"
    echo ""
    echo "📁 Backup file created: backup_transaction_summary_report_$(date +%Y%m%d_%H%M%S).csv"
else
    echo "❌ Migration failed"
    exit 1
fi
