const { Client } = require('pg');
const fs = require('fs');
const path = require('path');

// Database connection configuration
const connectionString = 'postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require';

async function runMigration() {
  const client = new Client({
    connectionString: connectionString
  });

  try {
    console.log('🚀 Starting database migration for transaction summary reports...');
    
    // Connect to database
    await client.connect();
    console.log('✅ Connected to database successfully');

    // Check if tables already exist
    const tableCheckResult = await client.query(`
      SELECT
        EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'transaction_summary_report') as report_table_exists,
        EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'transaction_summary_report_detail') as detail_table_exists
    `);

    const tableExists = tableCheckResult.rows[0];
    console.log('📊 Current table status:', tableExists);

    if (!tableExists.report_table_exists || !tableExists.detail_table_exists) {
      // Read and execute main migration
      console.log('📊 Running main migration: database_migration_create_transaction_summary_report.sql');
      const mainMigrationPath = path.join(__dirname, 'database_migration_create_transaction_summary_report.sql');

      if (fs.existsSync(mainMigrationPath)) {
        const mainMigrationSQL = fs.readFileSync(mainMigrationPath, 'utf8');
        await client.query(mainMigrationSQL);
        console.log('✅ Main migration completed successfully');
      } else {
        console.log('⚠️ Main migration file not found, skipping...');
      }
    } else {
      console.log('✅ Main tables already exist, skipping main migration');
    }

    // Read and execute enhanced workflow migration
    console.log('📊 Running enhanced workflow migration: database_migration_enhanced_workflow.sql');
    const enhancedMigrationPath = path.join(__dirname, 'database_migration_enhanced_workflow.sql');
    
    if (fs.existsSync(enhancedMigrationPath)) {
      const enhancedMigrationSQL = fs.readFileSync(enhancedMigrationPath, 'utf8');
      await client.query(enhancedMigrationSQL);
      console.log('✅ Enhanced workflow migration completed successfully');
    } else {
      console.log('⚠️ Enhanced workflow migration file not found, skipping...');
    }

    // Verify tables were created
    console.log('🔍 Verifying tables were created...');
    const finalTableCheckResult = await client.query(`
      SELECT
        EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'transaction_summary_report') as report_table_exists,
        EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'transaction_summary_report_detail') as detail_table_exists
    `);

    const finalTableExists = finalTableCheckResult.rows[0];
    console.log('📊 Table verification results:', finalTableExists);

    if (finalTableExists.report_table_exists && finalTableExists.detail_table_exists) {
      console.log('🎉 All migrations completed successfully!');
      console.log('📋 Summary of what was created:');
      console.log('  ✅ transaction_summary_report table');
      console.log('  ✅ transaction_summary_report_detail table');
      console.log('  ✅ Indexes for performance optimization');
      console.log('  ✅ Constraints for data integrity');
      console.log('  ✅ Views for monitoring');
      console.log('  ✅ Functions for statistics');
      console.log('');
      console.log('🚀 The enhanced workflow is now ready to use!');
      console.log('📝 Next steps:');
      console.log('  1. Restart the Electron application');
      console.log('  2. Upload transaction files via Transaction Management');
      console.log('  3. Process files to test the enhanced workflow');
      console.log('  4. Check Transaction Summary screen for real data');
    } else {
      console.log('❌ Some tables were not created properly');
      console.log('Please check the migration files and try again');
    }

  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    console.error('Full error:', error);
  } finally {
    await client.end();
    console.log('🔌 Database connection closed');
  }
}

// Run the migration
runMigration().catch(console.error);
