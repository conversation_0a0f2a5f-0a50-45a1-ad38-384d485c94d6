#!/bin/bash

# <PERSON>ript to run database migrations for transaction summary reports
# This script will create the necessary tables for the enhanced workflow

echo "🚀 Starting database migration for transaction summary reports..."

# Database connection string
DB_CONNECTION="postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require"

echo "📊 Running main migration: database_migration_create_transaction_summary_report.sql"
psql "$DB_CONNECTION" -f database_migration_create_transaction_summary_report.sql

if [ $? -eq 0 ]; then
    echo "✅ Main migration completed successfully"
else
    echo "❌ Main migration failed"
    exit 1
fi

echo "📊 Running enhanced workflow migration: database_migration_enhanced_workflow.sql"
psql "$DB_CONNECTION" -f database_migration_enhanced_workflow.sql

if [ $? -eq 0 ]; then
    echo "✅ Enhanced workflow migration completed successfully"
else
    echo "❌ Enhanced workflow migration failed"
    exit 1
fi

echo "🎉 All migrations completed successfully!"
echo "📋 Summary of what was created:"
echo "  ✅ transaction_summary_report table"
echo "  ✅ transaction_summary_report_detail table"
echo "  ✅ Indexes for performance optimization"
echo "  ✅ Constraints for data integrity"
echo "  ✅ Views for monitoring"
echo "  ✅ Functions for statistics"
echo ""
echo "🚀 The enhanced workflow is now ready to use!"
echo "📝 Next steps:"
echo "  1. Restart the Electron application"
echo "  2. Upload transaction files via Transaction Management"
echo "  3. Process files to test the enhanced workflow"
echo "  4. Check Transaction Summary screen for real data"
