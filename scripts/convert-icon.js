const fs = require('fs');
const path = require('path');

// Simple script to copy the PNG file to build directory
// For proper icon conversion, we'll use online tools or manual conversion

const sourceIcon = path.join(__dirname, '../src/assets/Epos.png');
const buildDir = path.join(__dirname, '../build');

// Ensure build directory exists
if (!fs.existsSync(buildDir)) {
  fs.mkdirSync(buildDir, { recursive: true });
}

// Copy PNG for Linux
const linuxIcon = path.join(buildDir, 'icon.png');
fs.copyFileSync(sourceIcon, linuxIcon);

console.log('✅ Copied Epos.png to build/icon.png for Linux');
console.log('');
console.log('📋 Next steps for complete icon setup:');
console.log('');
console.log('1. For Windows (.ico format):');
console.log('   - Visit: https://convertio.co/png-ico/');
console.log('   - Upload: src/assets/Epos.png');
console.log('   - Download and save as: build/icon.ico');
console.log('');
console.log('2. For macOS (.icns format):');
console.log('   - Visit: https://convertio.co/png-icns/');
console.log('   - Upload: src/assets/Epos.png');
console.log('   - Download and save as: build/icon.icns');
console.log('');
console.log('3. Alternative: Use iconutil on macOS:');
console.log('   mkdir icon.iconset');
console.log('   # Create multiple sizes and use iconutil -c icns icon.iconset');
console.log('');
console.log('🔧 The electron-builder.yml is already configured to use these files.');
