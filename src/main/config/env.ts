import * as dotenv from 'dotenv';
import * as path from 'path';

// Store the original environment variables before any dotenv loading
const originalNodeEnv = process.env.NODE_ENV;
const appEnv = process.env.APP_ENV;

// Function to load environment variables
const loadEnvironment = () => {
  // Determine which environment file to load based on APP_ENV
  // If APP_ENV is not set, fall back to NODE_ENV logic
  let envFile: string;
  if (appEnv === 'prod') {
    envFile = '.env.prod';
  } else if (appEnv === 'local') {
    envFile = '.env.local';
  } else {
    // Fallback to NODE_ENV logic
    const isDevelopment = originalNodeEnv !== 'production';
    envFile = isDevelopment ? '.env.local' : '.env.prod';
  }

  // Load environment variables from the appropriate file
  // Try multiple paths for different environments (development vs built app)
  const possiblePaths = [
    path.resolve(process.cwd(), envFile), // Development path
    path.join(process.resourcesPath, envFile), // Built app path
    path.join(__dirname, '../../', envFile), // Alternative development path
  ];

  console.log(`🔧 Loading environment from: ${envFile} (APP_ENV: ${appEnv}, NODE_ENV: ${originalNodeEnv})`);

  let result: any = { error: new Error('No valid path found') };

  // Try each possible path
  for (const envPath of possiblePaths) {
    try {
      result = dotenv.config({ path: envPath });
      if (!result.error) {
        console.log(`✅ Successfully loaded environment from: ${envPath}`);
        break;
      }
    } catch (error) {
      // Continue to next path
      continue;
    }
  }

  if (result.error) {
    console.warn(`⚠️ Warning: Could not load ${envFile} from any path:`, result.error.message);
    console.log(`🔍 Tried paths:`, possiblePaths);

    // Try to load .env as fallback
    const fallbackPaths = [
      path.resolve(process.cwd(), '.env'),
      path.join(process.resourcesPath, '.env'),
      path.join(__dirname, '../../', '.env'),
    ];

    for (const fallbackPath of fallbackPaths) {
      try {
        const fallbackResult = dotenv.config({ path: fallbackPath });
        if (!fallbackResult.error) {
          console.log(`✅ Loaded fallback environment from: ${fallbackPath}`);
          break;
        }
      } catch (error) {
        continue;
      }
    }
  }

  // Handle NODE_ENV properly based on the environment file loaded
  // If we loaded .env.prod, ensure NODE_ENV is set to production
  // If we loaded .env.local, ensure NODE_ENV is set to development
  if (envFile === '.env.prod') {
    process.env.NODE_ENV = 'production';
    console.log(`🔄 Set NODE_ENV to: production (from ${envFile})`);
  } else if (envFile === '.env.local') {
    process.env.NODE_ENV = 'development';
    console.log(`🔄 Set NODE_ENV to: development (from ${envFile})`);
  } else if (originalNodeEnv) {
    // For other cases, restore the original NODE_ENV
    process.env.NODE_ENV = originalNodeEnv;
    console.log(`🔄 Restored NODE_ENV to: ${originalNodeEnv}`);
  }

  // Log the final environment state
  console.log(`🎯 Final environment: NODE_ENV=${process.env.NODE_ENV}, APP_ENV=${appEnv}`);

  // Log database configuration for debugging (without sensitive data)
  console.log(`🗄️ Database config: ${process.env.DB_HOST}:${process.env.DB_PORT}/${process.env.DB_NAME} (SSL: ${process.env.DB_SSL})`);
};

// Load environment variables immediately
loadEnvironment();

// Database Configuration Interface
export interface DatabaseConfig {
  host: string;
  port: number;
  database: string;
  user: string;
  password: string;
  ssl: boolean;
  connectionTimeoutMillis: number;
  query_timeout: number;
}

// pCloud Configuration Interface
export interface PCloudConfig {
  username: string;
  password: string;
  region: 'us' | 'eu';
  remoteFolder: string;
  baseFolder: string;
  timeout: number;
  uploadTimeout: number;
}

// CSV Configuration Interface
export interface CSVConfig {
  folderPath: string;
  backupPath: string;
  autoProcessing: boolean;
  processDelay: number;
  enableBackup: boolean;
  enableCleanup: boolean;
  enablePCloudBackup: boolean;
}

// Application Configuration Interface
export interface AppConfig {
  name: string;
  version: string;
  nodeEnv: string;
  logLevel?: string;
  enableDebug?: boolean;
}

// Environment Configuration Class
export class EnvironmentConfig {
  private static instance: EnvironmentConfig;

  private constructor() {
    // Private constructor to enforce singleton pattern
  }

  public static getInstance(): EnvironmentConfig {
    if (!EnvironmentConfig.instance) {
      EnvironmentConfig.instance = new EnvironmentConfig();
    }
    return EnvironmentConfig.instance;
  }

  // Get Database Configuration
  public getDatabaseConfig(): DatabaseConfig {
    return {
      host: process.env.DB_HOST || 'localhost',
      port: parseInt(process.env.DB_PORT || '5432', 10),
      database: process.env.DB_NAME || 'postgres',
      user: process.env.DB_USER || 'postgres',
      password: process.env.DB_PASSWORD || '',
      ssl: process.env.DB_SSL === 'true',
      connectionTimeoutMillis: parseInt(process.env.DB_CONNECTION_TIMEOUT || '10000', 10),
      query_timeout: parseInt(process.env.DB_QUERY_TIMEOUT || '30000', 10),
    };
  }

  // Get pCloud Configuration
  public getPCloudConfig(): PCloudConfig {
    return {
      username: process.env.PCLOUD_USERNAME || '',
      password: process.env.PCLOUD_PASSWORD || '',
      region: (process.env.PCLOUD_REGION as 'us' | 'eu') || 'us',
      remoteFolder: process.env.PCLOUD_REMOTE_FOLDER || '/CSV_Backups',
      baseFolder: process.env.PCLOUD_BASE_FOLDER || '/csv_backup',
      timeout: parseInt(process.env.PCLOUD_TIMEOUT || '10000', 10),
      uploadTimeout: parseInt(process.env.PCLOUD_UPLOAD_TIMEOUT || '60000', 10),
    };
  }

  // Get CSV Configuration
  public getCSVConfig(): CSVConfig {
    return {
      folderPath: process.env.CSV_FOLDER_PATH || '/Users/<USER>/Desktop/simple_csv',
      backupPath: process.env.CSV_BACKUP_PATH || '/Users/<USER>/Desktop/simple_csv_bk',
      autoProcessing: process.env.CSV_AUTO_PROCESSING === 'true',
      processDelay: parseInt(process.env.CSV_PROCESS_DELAY || '2000', 10),
      enableBackup: process.env.CSV_ENABLE_BACKUP === 'true',
      enableCleanup: process.env.CSV_ENABLE_CLEANUP === 'true',
      enablePCloudBackup: process.env.CSV_ENABLE_PCLOUD_BACKUP === 'true',
    };
  }

  // Get Application Configuration
  public getAppConfig(): AppConfig {
    return {
      name: process.env.APP_NAME || 'Eposservice',
      version: process.env.APP_VERSION || '0.0.0',
      nodeEnv: process.env.NODE_ENV || 'development',
      logLevel: process.env.LOG_LEVEL,
      enableDebug: process.env.ENABLE_DEBUG === 'true',
    };
  }

  // Get specific environment variable
  public getEnv(key: string, defaultValue?: string): string {
    return process.env[key] || defaultValue || '';
  }

  // Check if running in development mode
  public isDevelopment(): boolean {
    return process.env.NODE_ENV !== 'production';
  }

  // Check if running in production mode
  public isProduction(): boolean {
    return process.env.NODE_ENV === 'production';
  }

  // Validate required environment variables
  public validateConfig(): { isValid: boolean; missingVars: string[] } {
    const requiredVars = [
      'DB_HOST',
      'DB_PORT',
      'DB_NAME',
      'DB_USER',
      'DB_PASSWORD',
      'PCLOUD_USERNAME',
      'PCLOUD_PASSWORD'
    ];

    const missingVars = requiredVars.filter(varName => !process.env[varName]);

    return {
      isValid: missingVars.length === 0,
      missingVars
    };
  }

  // Log current configuration (without sensitive data)
  public logConfig(): void {
    const dbConfig = this.getDatabaseConfig();
    const pcloudConfig = this.getPCloudConfig();
    const csvConfig = this.getCSVConfig();
    const appConfig = this.getAppConfig();

    console.log('🔧 Environment Configuration:');
    console.log(`📱 App: ${appConfig.name} v${appConfig.version} (${appConfig.nodeEnv})`);
    console.log(`🗄️ Database: ${dbConfig.host}:${dbConfig.port}/${dbConfig.database}`);
    console.log(`☁️ pCloud: ${pcloudConfig.username} (${pcloudConfig.region})`);
    console.log(`📁 CSV: ${csvConfig.folderPath} -> ${csvConfig.backupPath}`);
    console.log(`🔄 Auto Processing: ${csvConfig.autoProcessing}`);
    console.log(`☁️ pCloud Backup: ${csvConfig.enablePCloudBackup}`);
  }
}

// Export singleton instance
export const envConfig = EnvironmentConfig.getInstance();

// Export individual config getters for convenience
export const getDatabaseConfig = () => envConfig.getDatabaseConfig();
export const getPCloudConfig = () => envConfig.getPCloudConfig();
export const getCSVConfig = () => envConfig.getCSVConfig();
export const getAppConfig = () => envConfig.getAppConfig();
