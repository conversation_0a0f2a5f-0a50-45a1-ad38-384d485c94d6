import { ip<PERSON><PERSON><PERSON>, <PERSON><PERSON>erWindow } from 'electron';
import { Client } from 'pg';
import bcrypt from 'bcrypt';
import { randomUUID } from 'crypto';
import { getDbConnection, PostgreSQLConnectionConfig } from '../db';
import {
  getCurrentThailandDate,
  selectThailandTimestamp,
  getThailandTimeRange,
  isSessionExpired
} from '../utils/timezone';

interface UserRole {
  role_id: number;
  role_code: string;
  role_name: string;
  role_description?: string;
  active: boolean;
  create_by?: string;
  update_by?: string;
  create_dt: Date;
  update_dt: Date;
}

interface User {
  user_id: number;
  user_ref: string;
  user_name: string;
  user_pass: string;
  role_code?: string;
  active: boolean;
  locked: boolean;
  failed_attempts: number;
  last_login?: Date;
  create_by?: string;
  update_by?: string;
  create_dt: Date;
  update_dt: Date;
}

interface LoginRequest {
  username: string;
  password: string;
  ip_address?: string;
  user_agent?: string;
  force_login?: boolean; // New parameter to allow force login
}

interface LoginResponse {
  success: boolean;
  message: string;
  user?: {
    user_id: number;
    user_ref: string;
    user_name: string;
    role_code?: string;
    role_name?: string;
    last_login?: Date;
  };
  session_id?: string;
  expires_at?: Date;
  error?: string;
}

interface SessionValidationRequest {
  session_id: string;
  user_id: number;
}

interface SessionValidationResponse {
  valid: boolean;
  user?: {
    user_id: number;
    user_ref: string;
    user_name: string;
    role_code?: string;
    role_name?: string;
  };
  expires_at?: Date;
  message?: string;
}

export function setupAuthHandlers() {
  console.log('Setting up Authentication handlers...');

  // User login handler
  ipcMain.handle('user-login', async (_event, loginData: LoginRequest): Promise<LoginResponse> => {
    let client: Client | null = null;

    try {
      console.log('🔍 Processing login request for user:', loginData.username);
      console.log('🔗 Attempting database connection...');
      client = await getDbConnection();
      console.log('✅ Database connection established');

      console.log('🔍 Querying user from database...');
      const userResult = await client.query(`
        SELECT u.*, r.role_name
        FROM tsys_user u
        LEFT JOIN tmas_user_role r ON u.role_code = r.role_code
        WHERE u.user_name = $1
      `, [loginData.username]);
      console.log('📊 Query result:', userResult.rows.length, 'rows found');

      if (userResult.rows.length === 0) {
        await client.query(`
          INSERT INTO log_history_user (user_id, user_name, action_type, ip_address, user_agent, failure_reason, create_dt)
          VALUES (0, $1, 'LOGIN_FAILED', $2, $3, 'USER_NOT_FOUND', $4)
        `, [loginData.username, loginData.ip_address, loginData.user_agent, getCurrentThailandDate()]);

        return {
          success: false,
          message: 'Invalid username or password',
          error: 'USER_NOT_FOUND'
        };
      }

      const user = userResult.rows[0];

      if (!user.active) {
        await client.query(`
          INSERT INTO log_history_user (user_id, user_name, action_type, ip_address, user_agent, failure_reason, create_dt)
          VALUES ($1, $2, 'LOGIN_FAILED', $3, $4, 'ACCOUNT_INACTIVE', $5)
        `, [user.user_id, user.user_name, loginData.ip_address, loginData.user_agent, getCurrentThailandDate()]);

        return {
          success: false,
          message: 'Account is inactive',
          error: 'ACCOUNT_INACTIVE'
        };
      }

      if (user.locked) {
        await client.query(`
          INSERT INTO log_history_user (user_id, user_name, action_type, ip_address, user_agent, failure_reason, create_dt)
          VALUES ($1, $2, 'LOGIN_FAILED', $3, $4, 'ACCOUNT_LOCKED', $5)
        `, [user.user_id, user.user_name, loginData.ip_address, loginData.user_agent, getCurrentThailandDate()]);

        return {
          success: false,
          message: 'Account is locked due to multiple failed attempts',
          error: 'ACCOUNT_LOCKED'
        };
      }

      console.log('🔐 Verifying password...');
      let passwordValid = false;

      if (user.user_pass.startsWith('$2b$')) {
        passwordValid = await bcrypt.compare(loginData.password, user.user_pass);
        console.log('🔐 Used bcrypt comparison');
      } else {
        passwordValid = loginData.password === user.user_pass;
        console.log('🔐 Used plain text comparison');
      }

      console.log('🔐 Password validation result:', passwordValid);

      if (!passwordValid) {
        const newFailedAttempts = user.failed_attempts + 1;
        const shouldLock = newFailedAttempts >= 5;

        await client.query(`
          UPDATE tsys_user
          SET failed_attempts = $1, locked = $2, update_dt = $4
          WHERE user_id = $3
        `, [newFailedAttempts, shouldLock, user.user_id, getCurrentThailandDate()]);

        await client.query(`
          INSERT INTO log_history_user (user_id, user_name, action_type, ip_address, user_agent, failure_reason, attempt_count, create_dt)
          VALUES ($1, $2, 'LOGIN_FAILED', $3, $4, 'WRONG_PASSWORD', $5, $6)
        `, [user.user_id, user.user_name, loginData.ip_address, loginData.user_agent, newFailedAttempts, getCurrentThailandDate()]);

        return {
          success: false,
          message: shouldLock ? 'Account locked due to multiple failed attempts' : 'Invalid username or password',
          error: 'WRONG_PASSWORD'
        };
      }

      // Check for existing active sessions (single session per user enforcement)
      console.log('🔍 Checking for existing active sessions...');

      // First, find all recent login sessions (within 24 hours for broader search)
      const recentSessionsResult = await client.query(`
        SELECT l.session_id, ${selectThailandTimestamp('l.create_dt')}, l.ip_address
        FROM log_history_user l
        WHERE l.user_id = $1
        AND l.action_type = 'LOGIN_SUCCESS'
        AND ${getThailandTimeRange('l.create_dt', 24)}
        ORDER BY l.create_dt DESC
      `, [user.user_id]);

      console.log(`📊 Found ${recentSessionsResult.rows.length} recent login sessions`);

      // Check each session to see if it's truly active
      let activeSession = null;
      for (const session of recentSessionsResult.rows) {
        const sessionTime = session.create_dt_thai;
        const sessionAge = Math.floor((Date.now() - sessionTime.getTime()) / 1000 / 60); // minutes

        console.log(`🔍 Checking session ${session.session_id} (${sessionAge} minutes old)`);

        // Check if session is expired (older than 1 hour)
        if (isSessionExpired(sessionTime, 1)) {
          console.log(`⏰ Session ${session.session_id} expired (older than 1 hour)`);
          continue;
        }

        // Check if session was logged out
        const logoutCheck = await client.query(`
          SELECT 1
          FROM log_history_user logout_log
          WHERE logout_log.user_id = $1
          AND logout_log.session_id = $2
          AND logout_log.action_type IN ('LOGOUT', 'FORCE_LOGOUT')
          AND logout_log.create_dt > $3
          LIMIT 1
        `, [user.user_id, session.session_id, sessionTime]);

        if (logoutCheck.rows.length > 0) {
          console.log(`🚪 Session ${session.session_id} was logged out`);
          continue;
        }

        // This session is active (not expired and not logged out)
        activeSession = session;
        console.log(`✅ Found active session: ${session.session_id}`);
        break;
      }

      const existingSessionResult = { rows: activeSession ? [activeSession] : [] };

      if (existingSessionResult.rows.length > 0) {
        const existingSession = existingSessionResult.rows[0];
        const sessionAge = Math.floor((Date.now() - existingSession.create_dt_thai.getTime()) / 1000 / 60); // minutes

        console.log(`⚠️ User ${user.user_name} already has an active session:`);
        console.log(`   Session ID: ${existingSession.session_id}`);
        console.log(`   IP Address: ${existingSession.ip_address || 'unknown IP'}`);
        console.log(`   Login Time: ${existingSession.create_dt_thai}`);
        console.log(`   Minutes Ago: ${sessionAge}`);

        // If force_login is not true, reject the login
        if (!loginData.force_login) {
          await client.query(`
            INSERT INTO log_history_user (user_id, user_name, action_type, ip_address, user_agent, failure_reason, session_id, create_dt)
            VALUES ($1, $2, 'LOGIN_FAILED', $3, $4, 'ALREADY_LOGGED_IN', $5, $6)
          `, [user.user_id, user.user_name, loginData.ip_address, loginData.user_agent, existingSession.session_id, getCurrentThailandDate()]);

          return {
            success: false,
            message: `User is already logged in from another session (${sessionAge} minutes ago). Use force login to terminate existing session.`,
            error: 'ALREADY_LOGGED_IN'
          };
        } else {
          // Force login: terminate existing session
          console.log(`🔄 Force login requested, terminating existing session for ${user.user_name}`);
          
          await client.query(`
            INSERT INTO log_history_user (user_id, user_name, action_type, ip_address, user_agent, session_id, failure_reason, create_dt)
            VALUES ($1, $2, 'FORCE_LOGOUT', $3, $4, $5, 'TERMINATED_BY_FORCE_LOGIN', $6)
          `, [user.user_id, user.user_name, loginData.ip_address, loginData.user_agent, existingSession.session_id, getCurrentThailandDate()]);
          
          console.log(`✅ Existing session terminated for force login`);
        }
      }

      const sessionId = randomUUID();
      const expiresAt = new Date(Date.now() + 1 * 60 * 60 * 1000); // 1 hour expiry

      const currentTime = getCurrentThailandDate();
      await client.query(`
        UPDATE tsys_user
        SET failed_attempts = 0, last_login = $2, update_dt = $3
        WHERE user_id = $1
      `, [user.user_id, currentTime, currentTime]);

      await client.query(`
        INSERT INTO log_history_user (user_id, user_name, action_type, ip_address, user_agent, session_id, create_dt)
        VALUES ($1, $2, 'LOGIN_SUCCESS', $3, $4, $5, $6)
      `, [user.user_id, user.user_name, loginData.ip_address, loginData.user_agent, sessionId, currentTime]);

      console.log('✅ User login successful:', user.user_name);
      return {
        success: true,
        message: 'Login successful',
        user: {
          user_id: user.user_id,
          user_ref: user.user_ref,
          user_name: user.user_name,
          role_code: user.role_code,
          role_name: user.role_name,
          last_login: user.last_login
        },
        session_id: sessionId,
        expires_at: expiresAt
      };

    } catch (error: any) {
      console.error('❌ Error during login:', error.message);
      return {
        success: false,
        message: 'Login failed due to server error',
        error: error.message
      };
    } finally {
      if (client) {
        await client.end();
      }
    }
  });

  // Session validation handler
  ipcMain.handle('validate-session', async (_event, sessionData: SessionValidationRequest): Promise<SessionValidationResponse> => {
    let client: Client | null = null;

    try {
      console.log('🔍 Validating session for user:', sessionData.user_id);
      client = await getDbConnection();

      const userResult = await client.query(`
        SELECT u.*, r.role_name
        FROM tsys_user u
        LEFT JOIN tmas_user_role r ON u.role_code = r.role_code
        WHERE u.user_id = $1 AND u.active = true AND u.locked = false
      `, [sessionData.user_id]);

      if (userResult.rows.length === 0) {
        return {
          valid: false,
          message: 'User not found or account is inactive/locked'
        };
      }

      const user = userResult.rows[0];

      console.log('🔍 Looking for session:', sessionData.session_id, 'for user:', sessionData.user_id);
      const sessionResult = await client.query(`
        SELECT *, ${selectThailandTimestamp('create_dt')} FROM log_history_user
        WHERE user_id = $1 AND session_id = $2 AND action_type = 'LOGIN_SUCCESS'
        ORDER BY create_dt DESC
        LIMIT 1
      `, [sessionData.user_id, sessionData.session_id]);

      console.log('📊 Session query result:', sessionResult.rows.length, 'rows found');

      if (sessionResult.rows.length === 0) {
        return {
          valid: false,
          message: 'Invalid session - session not found'
        };
      }

      const sessionLog = sessionResult.rows[0];
      const sessionTime = sessionLog.create_dt_thai;

      // Check if session is expired (older than 1 hour)
      if (isSessionExpired(sessionTime, 1)) {
        console.log('⏰ Session expired (older than 1 hour)');
        return {
          valid: false,
          message: 'Session expired - please login again'
        };
      }

      // Check if session was logged out
      const logoutCheck = await client.query(`
        SELECT 1
        FROM log_history_user
        WHERE user_id = $1
        AND session_id = $2
        AND action_type IN ('LOGOUT', 'FORCE_LOGOUT')
        AND create_dt > $3
        LIMIT 1
      `, [sessionData.user_id, sessionData.session_id, sessionTime]);

      if (logoutCheck.rows.length > 0) {
        console.log('🚪 Session was logged out');
        return {
          valid: false,
          message: 'Session terminated - please login again'
        };
      }

      const expiresAt = new Date(sessionTime.getTime() + 1 * 60 * 60 * 1000); // 1 hour expiry

      console.log('✅ Session validated successfully for user:', user.user_name);
      return {
        valid: true,
        user: {
          user_id: user.user_id,
          user_ref: user.user_ref,
          user_name: user.user_name,
          role_code: user.role_code,
          role_name: user.role_name
        },
        expires_at: expiresAt,
        message: 'Session is valid'
      };

    } catch (error: any) {
      console.error('❌ Error validating session:', error.message);
      return {
        valid: false,
        message: 'Session validation failed due to server error'
      };
    } finally {
      if (client) {
        await client.end();
      }
    }
  });

  // User logout handler
  ipcMain.handle('user-logout', async (_event, logoutData: { user_id: number; session_id: string; ip_address?: string; user_agent?: string }): Promise<{ success: boolean; message: string }> => {
    let client: Client | null = null;

    try {
      console.log('🚪 Processing logout for user:', logoutData.user_id, 'session:', logoutData.session_id);
      client = await getDbConnection();

      // Get user name for logging
      const userResult = await client.query(`
        SELECT user_name FROM tsys_user WHERE user_id = $1
      `, [logoutData.user_id]);

      const userName = userResult.rows[0]?.user_name || 'UNKNOWN';

      // Log the logout action
      const logoutResult = await client.query(`
        INSERT INTO log_history_user (user_id, user_name, action_type, ip_address, user_agent, session_id, create_dt)
        VALUES ($1, $2, 'LOGOUT', $3, $4, $5, $6)
        RETURNING *
      `, [logoutData.user_id, userName, logoutData.ip_address, logoutData.user_agent, logoutData.session_id, getCurrentThailandDate()]);

      console.log('✅ User logout logged successfully:', userName, 'logout record ID:', logoutResult.rows[0]?.id);
      console.log('📝 Logout details:', {
        user_id: logoutData.user_id,
        session_id: logoutData.session_id,
        timestamp: getCurrentThailandDate()
      });

      return {
        success: true,
        message: 'Logout successful'
      };

    } catch (error: any) {
      console.error('❌ Error during logout:', error.message);
      return {
        success: false,
        message: 'Logout failed due to server error'
      };
    } finally {
      if (client) {
        await client.end();
      }
    }
  });

  // Get all users handler with search and pagination
  ipcMain.handle('get-users', async (_event, options: {
    page?: number;
    pageSize?: number;
    search?: string;
  } = {}): Promise<{ 
    success: boolean; 
    users?: any[]; 
    pagination?: {
      currentPage: number;
      totalPages: number;
      totalRecords: number;
      hasNextPage: boolean;
      hasPreviousPage: boolean;
    };
    message?: string; 
    error?: string 
  }> => {
    let client: Client | null = null;

    try {
      const { page = 1, pageSize = 10, search = '' } = options;
      const offset = (page - 1) * pageSize;

      console.log('📋 [GET-USERS] Handler called with options:', { page, pageSize, search });
      console.log('📋 [GET-USERS] Calculated offset:', offset);
      
      client = await getDbConnection();
      console.log('📋 [GET-USERS] Database connection established');

      // Build the WHERE clause for search
      let whereClause = '';
      let searchParams: any[] = [];
      
      if (search && search.trim()) {
        whereClause = 'WHERE LOWER(u.user_name) LIKE LOWER($1) OR LOWER(u.user_ref::text) LIKE LOWER($1)';
        searchParams.push(`%${search.trim()}%`);
        console.log('📋 [GET-USERS] Search parameters:', searchParams);
      }

      // Get total count for pagination
      const countQuery = `
        SELECT COUNT(*) as total
        FROM tsys_user u
        LEFT JOIN tmas_user_role r ON u.role_code = r.role_code
        ${whereClause}
      `;

      console.log('📋 [GET-USERS] Count query:', countQuery);
      console.log('📋 [GET-USERS] Count params:', searchParams);
      
      const countResult = await client.query(countQuery, searchParams);
      const totalRecords = parseInt(countResult.rows[0].total);
      const totalPages = Math.ceil(totalRecords / pageSize);

      console.log('📋 [GET-USERS] Count result - total records:', totalRecords, 'total pages:', totalPages);

      // Get paginated results
      const dataQuery = `
        SELECT u.user_id, u.user_ref, u.user_name, u.role_code, r.role_name,
               u.active, u.locked, u.failed_attempts, 
               u.last_login,
               u.create_dt, 
               u.update_dt
        FROM tsys_user u
        LEFT JOIN tmas_user_role r ON u.role_code = r.role_code
        ${whereClause}
        ORDER BY u.user_name
        LIMIT $${searchParams.length + 1} OFFSET $${searchParams.length + 2}
      `;

      const dataParams = [...searchParams, pageSize, offset];
      console.log('📋 [GET-USERS] Data query:', dataQuery);
      console.log('📋 [GET-USERS] Data params:', dataParams);
      
      const result = await client.query(dataQuery, dataParams);

      const pagination = {
        currentPage: page,
        totalPages,
        totalRecords,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1
      };

      console.log(`✅ [GET-USERS] Found ${result.rows.length} users (page ${page} of ${totalPages})`);
      console.log('✅ [GET-USERS] Pagination:', pagination);
      
      return {
        success: true,
        users: result.rows,
        pagination,
        message: `Found ${totalRecords} users`
      };

    } catch (error: any) {
      console.error('❌ [GET-USERS] Error fetching users:', error.message);
      console.error('❌ [GET-USERS] Error stack:', error.stack);
      return {
        success: false,
        message: 'Failed to fetch users',
        error: error.message
      };
    } finally {
      if (client) {
        console.log('📋 [GET-USERS] Closing database connection');
        await client.end();
      }
    }
  });

  // Check user session status
  ipcMain.handle('check-user-session-status', async (_event, username: string): Promise<{ 
    success: boolean; 
    has_active_session: boolean; 
    session_info?: { 
      session_id: string; 
      login_time: Date; 
      ip_address?: string; 
      minutes_ago: number 
    }; 
    message: string 
  }> => {
    let client: Client | null = null;

    try {
      console.log('🔍 Checking session status for user:', username);
      client = await getDbConnection();

      // Get user information
      const userResult = await client.query(`
        SELECT user_id, user_name FROM tsys_user WHERE user_name = $1
      `, [username]);

      if (userResult.rows.length === 0) {
        return {
          success: false,
          has_active_session: false,
          message: 'User not found'
        };
      }

      const user = userResult.rows[0];

      // Check for active sessions using improved logic
      const recentSessionsResult = await client.query(`
        SELECT l.session_id, ${selectThailandTimestamp('l.create_dt')}, l.ip_address
        FROM log_history_user l
        WHERE l.user_id = $1
        AND l.action_type = 'LOGIN_SUCCESS'
        AND ${getThailandTimeRange('l.create_dt', 24)}
        ORDER BY l.create_dt DESC
      `, [user.user_id]);

      // Check each session to see if it's truly active (not expired and not logged out)
      let activeSession = null;
      for (const session of recentSessionsResult.rows) {
        const sessionTime = session.create_dt_thai;

        // Check if session is expired (older than 1 hour)
        if (isSessionExpired(sessionTime, 1)) {
          continue;
        }

        // Check if session was logged out
        const logoutCheck = await client.query(`
          SELECT 1
          FROM log_history_user logout_log
          WHERE logout_log.user_id = $1
          AND logout_log.session_id = $2
          AND logout_log.action_type IN ('LOGOUT', 'FORCE_LOGOUT')
          AND logout_log.create_dt > $3
          LIMIT 1
        `, [user.user_id, session.session_id, sessionTime]);

        if (logoutCheck.rows.length === 0) {
          // This session is active (not expired and not logged out)
          activeSession = session;
          break;
        }
      }

      const activeSessionResult = { rows: activeSession ? [activeSession] : [] };

      if (activeSessionResult.rows.length === 0) {
        return {
          success: true,
          has_active_session: false,
          message: 'No active sessions found'
        };
      }

      const foundSession = activeSessionResult.rows[0];
      const minutesAgo = Math.floor((Date.now() - foundSession.create_dt_thai.getTime()) / 1000 / 60);

      return {
        success: true,
        has_active_session: true,
        session_info: {
          session_id: foundSession.session_id,
          login_time: foundSession.create_dt_thai,
          ip_address: foundSession.ip_address,
          minutes_ago: minutesAgo
        },
        message: `User has an active session from ${minutesAgo} minutes ago`
      };

    } catch (error: any) {
      console.error('❌ Error checking user session status:', error.message);
      return {
        success: false,
        has_active_session: false,
        message: 'Failed to check session status'
      };
    } finally {
      if (client) {
        await client.end();
      }
    }
  });

  // Force logout all sessions for a user
  ipcMain.handle('force-logout-user', async (_event, forceLogoutData: { 
    user_name: string; 
    ip_address?: string; 
    user_agent?: string 
  }): Promise<{ success: boolean; message: string; sessions_terminated?: number }> => {
    let client: Client | null = null;

    try {
      console.log('🚪 Processing force logout for user:', forceLogoutData.user_name);
      client = await getDbConnection();

      // Get user information
      const userResult = await client.query(`
        SELECT user_id, user_name FROM tsys_user WHERE user_name = $1
      `, [forceLogoutData.user_name]);

      if (userResult.rows.length === 0) {
        return {
          success: false,
          message: 'User not found'
        };
      }

      const user = userResult.rows[0];

      // Find all active sessions for this user using improved logic
      const recentSessionsResult = await client.query(`
        SELECT DISTINCT l.session_id, ${selectThailandTimestamp('l.create_dt')}, l.ip_address
        FROM log_history_user l
        WHERE l.user_id = $1
        AND l.action_type = 'LOGIN_SUCCESS'
        AND ${getThailandTimeRange('l.create_dt', 24)}
        ORDER BY l.create_dt DESC
      `, [user.user_id]);

      // Filter to only truly active sessions (not expired and not logged out)
      const activeSessions = [];
      for (const session of recentSessionsResult.rows) {
        const sessionTime = session.create_dt_thai;

        // Check if session is expired (older than 1 hour)
        if (isSessionExpired(sessionTime, 1)) {
          continue;
        }

        // Check if session was logged out
        const logoutCheck = await client.query(`
          SELECT 1
          FROM log_history_user logout_log
          WHERE logout_log.user_id = $1
          AND logout_log.session_id = $2
          AND logout_log.action_type IN ('LOGOUT', 'FORCE_LOGOUT')
          AND logout_log.create_dt > $3
          LIMIT 1
        `, [user.user_id, session.session_id, sessionTime]);

        if (logoutCheck.rows.length === 0) {
          // This session is active (not expired and not logged out)
          activeSessions.push(session);
        }
      }
      console.log(`📊 Found ${activeSessions.length} active sessions for user ${user.user_name}`);

      // Log forced logout for each active session
      for (const session of activeSessions) {
        await client.query(`
          INSERT INTO log_history_user (user_id, user_name, action_type, ip_address, user_agent, session_id, failure_reason, create_dt)
          VALUES ($1, $2, 'FORCE_LOGOUT', $3, $4, $5, 'TERMINATED_BY_NEW_LOGIN', $6)
        `, [user.user_id, user.user_name, forceLogoutData.ip_address, forceLogoutData.user_agent, session.session_id, getCurrentThailandDate()]);
      }

      console.log(`✅ Force logout completed for user ${user.user_name}, terminated ${activeSessions.length} sessions`);
      return {
        success: true,
        message: `Successfully terminated ${activeSessions.length} active session(s)`,
        sessions_terminated: activeSessions.length
      };

    } catch (error: any) {
      console.error('❌ Error during force logout:', error.message);
      return {
        success: false,
        message: 'Force logout failed due to server error'
      };
    } finally {
      if (client) {
        await client.end();
      }
    }
  });

  // Get all roles handler
  ipcMain.handle('get-roles', async (): Promise<{ success: boolean; roles?: any[]; message?: string; error?: string }> => {
    let client: Client | null = null;

    try {
      console.log('📋 Fetching all roles...');
      client = await getDbConnection();

      const result = await client.query(`
        SELECT role_id, role_code, role_name, role_description, active, 
               ${selectThailandTimestamp('create_dt')}, 
               ${selectThailandTimestamp('update_dt')}
        FROM tmas_user_role
        ORDER BY role_name
      `);

      console.log(`✅ Found ${result.rows.length} roles`);
      return {
        success: true,
        roles: result.rows,
        message: `Found ${result.rows.length} roles`
      };

    } catch (error: any) {
      console.error('❌ Error fetching roles:', error.message);
      return {
        success: false,
        message: 'Failed to fetch roles',
        error: error.message
      };
    } finally {
      if (client) {
        await client.end();
      }
    }
  });

  // Create user handler
  ipcMain.handle('create-user', async (_event, userData: {
    user_name: string;
    password: string;
    role_code?: string;
    active?: boolean;
    created_by?: string;
  }): Promise<{ success: boolean; message: string; user?: any; error?: string }> => {
    let client: Client | null = null;

    try {
      console.log('🔍 Processing user creation request for:', userData.user_name);
      client = await getDbConnection();

      // Check if username already exists
      const existingUser = await client.query(`
        SELECT user_name FROM tsys_user WHERE user_name = $1
      `, [userData.user_name]);

      if (existingUser.rows.length > 0) {
        return {
          success: false,
          message: 'Username already exists',
          error: 'USERNAME_EXISTS'
        };
      }

      // Generate user reference
      const userRef = randomUUID();

      // Encrypt password
      const saltRounds = 10;
      const hashedPassword = await bcrypt.hash(userData.password, saltRounds);

      // Insert new user
      const currentTime = getCurrentThailandDate();
      const result = await client.query(`
        INSERT INTO tsys_user (
          user_ref, user_name, user_pass, role_code, active, locked, failed_attempts,
          create_by, update_by, create_dt, update_dt
        )
        VALUES ($1, $2, $3, $4, $5, false, 0, $6, $6, $7, $8)
        RETURNING user_id, user_ref, user_name, role_code, active,
                  ${selectThailandTimestamp('create_dt')}
      `, [
        userRef,
        userData.user_name,
        hashedPassword,
        userData.role_code || null,
        userData.active !== false, // Default to true unless explicitly false
        userData.created_by || 'SYSTEM',
        currentTime,
        currentTime
      ]);

      const newUser = result.rows[0];

      console.log('✅ User created successfully:', newUser.user_name);
      return {
        success: true,
        message: 'User created successfully',
        user: newUser
      };

    } catch (error: any) {
      console.error('❌ Error creating user:', error.message);
      return {
        success: false,
        message: 'Failed to create user',
        error: error.message
      };
    } finally {
      if (client) {
        await client.end();
      }
    }
  });

  // Update user handler
  ipcMain.handle('update-user', async (_event, userId: number, userData: {
    user_name?: string;
    password?: string;
    role_code?: string;
    active?: boolean;
    locked?: boolean;
    updated_by?: string;
  }): Promise<{ success: boolean; message: string; user?: any; error?: string }> => {
    let client: Client | null = null;

    try {
      console.log('🔍 Processing user update request for user ID:', userId);
      client = await getDbConnection();

      // Check if user exists
      const existingUser = await client.query(`
        SELECT * FROM tsys_user WHERE user_id = $1
      `, [userId]);

      if (existingUser.rows.length === 0) {
        return {
          success: false,
          message: 'User not found',
          error: 'USER_NOT_FOUND'
        };
      }

      // If username is being updated, check for duplicates
      if (userData.user_name) {
        const duplicateCheck = await client.query(`
          SELECT user_name FROM tsys_user WHERE user_name = $1 AND user_id != $2
        `, [userData.user_name, userId]);

        if (duplicateCheck.rows.length > 0) {
          return {
            success: false,
            message: 'Username already exists',
            error: 'USERNAME_EXISTS'
          };
        }
      }

      // Build update query dynamically
      const updateFields: string[] = [];
      const updateValues: any[] = [];
      let paramCounter = 1;

      if (userData.user_name !== undefined) {
        updateFields.push(`user_name = $${paramCounter++}`);
        updateValues.push(userData.user_name);
      }

      if (userData.password !== undefined && userData.password.trim() !== '') {
        const saltRounds = 10;
        const hashedPassword = await bcrypt.hash(userData.password, saltRounds);
        updateFields.push(`user_pass = $${paramCounter++}`);
        updateValues.push(hashedPassword);
      }

      if (userData.role_code !== undefined) {
        updateFields.push(`role_code = $${paramCounter++}`);
        updateValues.push(userData.role_code || null);
      }

      if (userData.active !== undefined) {
        updateFields.push(`active = $${paramCounter++}`);
        updateValues.push(userData.active);
      }

      if (userData.locked !== undefined) {
        updateFields.push(`locked = $${paramCounter++}`);
        updateValues.push(userData.locked);
        
        // Reset failed attempts when unlocking
        if (!userData.locked) {
          updateFields.push(`failed_attempts = $${paramCounter++}`);
          updateValues.push(0);
        }
      }

      updateFields.push(`update_by = $${paramCounter++}`);
      updateValues.push(userData.updated_by || 'SYSTEM');

      updateFields.push(`update_dt = $${paramCounter++}`);
      updateValues.push(getCurrentThailandDate());

      // Add user_id for WHERE clause
      updateValues.push(userId);

      const updateQuery = `
        UPDATE tsys_user 
        SET ${updateFields.join(', ')}
        WHERE user_id = $${paramCounter}
        RETURNING user_id, user_ref, user_name, role_code, active, locked, failed_attempts, 
                  ${selectThailandTimestamp('update_dt')}
      `;

      const result = await client.query(updateQuery, updateValues);
      const updatedUser = result.rows[0];

      console.log('✅ User updated successfully:', updatedUser.user_name);
      return {
        success: true,
        message: 'User updated successfully',
        user: updatedUser
      };

    } catch (error: any) {
      console.error('❌ Error updating user:', error.message);
      return {
        success: false,
        message: 'Failed to update user',
        error: error.message
      };
    } finally {
      if (client) {
        await client.end();
      }
    }
  });

  // Delete user handler
  ipcMain.handle('delete-user', async (_event, userId: number): Promise<{ success: boolean; message: string; error?: string }> => {
    let client: Client | null = null;

    try {
      console.log('🔍 Processing user deletion request for user ID:', userId);
      client = await getDbConnection();

      // Check if user exists
      const existingUser = await client.query(`
        SELECT user_name FROM tsys_user WHERE user_id = $1
      `, [userId]);

      if (existingUser.rows.length === 0) {
        return {
          success: false,
          message: 'User not found',
          error: 'USER_NOT_FOUND'
        };
      }

      const userName = existingUser.rows[0].user_name;

      // Delete user (consider soft delete by setting active=false instead)
      await client.query(`
        UPDATE tsys_user
        SET active = false, update_dt = $2, update_by = 'SYSTEM'
        WHERE user_id = $1
      `, [userId, getCurrentThailandDate()]);

      console.log('✅ User deactivated successfully:', userName);
      return {
        success: true,
        message: 'User deactivated successfully'
      };

    } catch (error: any) {
      console.error('❌ Error deleting user:', error.message);
      return {
        success: false,
        message: 'Failed to delete user',
        error: error.message
      };
    } finally {
      if (client) {
        await client.end();
      }
    }
  });

  // Reset user password handler
  ipcMain.handle('reset-user-password', async (_event, userId: number, newPassword: string): Promise<{ success: boolean; message: string; error?: string }> => {
    let client: Client | null = null;

    try {
      console.log('🔍 Processing password reset request for user ID:', userId);
      client = await getDbConnection();

      // Check if user exists
      const existingUser = await client.query(`
        SELECT user_name FROM tsys_user WHERE user_id = $1
      `, [userId]);

      if (existingUser.rows.length === 0) {
        return {
          success: false,
          message: 'User not found',
          error: 'USER_NOT_FOUND'
        };
      }

      // Encrypt new password
      const saltRounds = 10;
      const hashedPassword = await bcrypt.hash(newPassword, saltRounds);

      // Update password and reset failed attempts
      await client.query(`
        UPDATE tsys_user
        SET user_pass = $1, failed_attempts = 0, locked = false, update_dt = $3, update_by = 'SYSTEM'
        WHERE user_id = $2
      `, [hashedPassword, userId, getCurrentThailandDate()]);

      const userName = existingUser.rows[0].user_name;

      console.log('✅ Password reset successfully for user:', userName);
      return {
        success: true,
        message: 'Password reset successfully'
      };

    } catch (error: any) {
      console.error('❌ Error resetting password:', error.message);
      return {
        success: false,
        message: 'Failed to reset password',
        error: error.message
      };
    } finally {
      if (client) {
        await client.end();
      }
    }
  });

  // Handle logout confirmation from renderer process
  ipcMain.handle('confirm-logout-and-exit', async (_event): Promise<{ success: boolean; message: string }> => {
    try {
      console.log('🚪 Processing logout confirmation from renderer');

      // Get the main window
      const mainWindow = BrowserWindow.getAllWindows()[0];
      if (!mainWindow) {
        return {
          success: false,
          message: 'Main window not found'
        };
      }

      // Get current authentication state from renderer
      const authState = await mainWindow.webContents.executeJavaScript(`
        (function() {
          try {
            const user = localStorage.getItem('auth_user');
            const sessionId = localStorage.getItem('auth_session_id');
            return {
              user: user ? JSON.parse(user) : null,
              sessionId: sessionId
            };
          } catch (error) {
            return { user: null, sessionId: null };
          }
        })()
      `);

      // If user is authenticated, perform logout
      if (authState.user && authState.sessionId) {
        console.log('🔐 User is authenticated, performing logout for:', authState.user.user_name);

        // Call the existing logout handler
        const logoutResult = await new Promise((resolve) => {
          const logoutHandler = ipcMain.listeners('user-logout')[0] as any;
          if (logoutHandler) {
            logoutHandler(null, {
              user_id: authState.user.user_id,
              session_id: authState.sessionId,
              ip_address: 'localhost',
              user_agent: 'Electron App Close'
            }).then(resolve).catch(resolve);
          } else {
            resolve({ success: false, message: 'Logout handler not found' });
          }
        });

        console.log('📝 Logout result:', logoutResult);
      }

      // Clear authentication state in renderer
      await mainWindow.webContents.executeJavaScript(`
        (function() {
          try {
            localStorage.removeItem('auth_user');
            localStorage.removeItem('auth_session_id');
            localStorage.removeItem('auth_expires_at');
            console.log('🧹 Authentication state cleared');
          } catch (error) {
            console.error('Error clearing auth state:', error);
          }
        })()
      `);

      return {
        success: true,
        message: 'Logout completed successfully'
      };

    } catch (error: any) {
      console.error('❌ Error during logout confirmation:', error.message);
      return {
        success: false,
        message: 'Logout failed due to error'
      };
    }
  });

  console.log('✅ All Authentication handlers registered successfully');
}
