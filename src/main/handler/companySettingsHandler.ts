import { ipcMain } from 'electron';
import { Client } from 'pg';
import { getDbConnection } from '../db';

interface CompanySettings {
  id?: number;
  // Company Information
  company_name_th: string;
  company_name_en: string;
  contact_name: string;
  tax_id?: string;
  iin?: string;
  
  // Address Information
  address_th?: string;
  address_en?: string;
  
  // Contact Information
  telephone?: string;
  fax?: string;
  email?: string;
  website?: string;
  
  // Additional Information
  additional_info?: string;
  
  // Banking Information
  bank_name?: string;
  branch?: string;
  rate?: string;
  
  // Company Logo
  logo_path?: string;
  
  // Status
  active: boolean;
  create_by: string;
  create_dt?: string;
  update_by?: string;
  update_dt?: string;
}

interface CompanySettingsResponse {
  success: boolean;
  message: string;
  data?: CompanySettings;
  error?: string;
}

export function setupCompanySettingsHandlers() {
  console.log('Setting up Company Settings handlers...');

  // Initialize company_setting table
  ipcMain.handle('init-company-setting-table', async (): Promise<CompanySettingsResponse> => {
    let client: Client | null = null;

    try {
      console.log('🔧 Initializing company_setting table...');
      client = await getDbConnection();

      const createTableQuery = `
        CREATE TABLE IF NOT EXISTS company_setting (
          id SERIAL PRIMARY KEY,
          
          -- Company Information
          company_name_th VARCHAR(255) NOT NULL,
          company_name_en VARCHAR(255) NOT NULL,
          contact_name VARCHAR(255) NOT NULL,
          tax_id VARCHAR(50),
          iin VARCHAR(50),
          
          -- Address Information
          address_th TEXT,
          address_en TEXT,
          
          -- Contact Information
          telephone VARCHAR(50),
          fax VARCHAR(50),
          email VARCHAR(255),
          website VARCHAR(255),
          
          -- Additional Information
          additional_info TEXT,
          
          -- Banking Information
          bank_name VARCHAR(100),
          branch VARCHAR(100),
          rate VARCHAR(50),
          
          -- Company Logo
          logo_path TEXT,
          
          -- Status
          active BOOLEAN NOT NULL DEFAULT TRUE,
          create_by VARCHAR(100) NOT NULL,
          create_dt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          update_by VARCHAR(100),
          update_dt TIMESTAMP
        );
      `;

      await client.query(createTableQuery);
      console.log('✅ Company setting table initialized successfully');

      return {
        success: true,
        message: 'Company setting table initialized successfully'
      };

    } catch (error: any) {
      console.error('❌ Error initializing company setting table:', error.message);
      return {
        success: false,
        message: 'Failed to initialize company setting table',
        error: error.message
      };
    } finally {
      if (client) {
        await client.end();
      }
    }
  });

  // Get company settings (should return single record)
  ipcMain.handle('get-company-settings', async (): Promise<CompanySettingsResponse> => {
    let client: Client | null = null;

    try {
      console.log('📖 Fetching company settings...');
      client = await getDbConnection();

      const query = `
        SELECT id, company_name_th, company_name_en, contact_name, tax_id, iin,
               address_th, address_en, telephone, fax, email, website,
               additional_info, bank_name, branch, rate, logo_path, active,
               create_by, create_dt, update_by, update_dt
        FROM company_setting
        ORDER BY id DESC
        LIMIT 1
      `;

      const result = await client.query(query);

      if (result.rows.length === 0) {
        console.log('ℹ️ No company settings found');
        return {
          success: true,
          message: 'No company settings found',
          data: undefined
        };
      }

      console.log('✅ Company settings retrieved successfully');
      return {
        success: true,
        message: 'Company settings retrieved successfully',
        data: result.rows[0]
      };

    } catch (error: any) {
      console.error('❌ Error fetching company settings:', error.message);
      return {
        success: false,
        message: 'Failed to fetch company settings',
        error: error.message
      };
    } finally {
      if (client) {
        await client.end();
      }
    }
  });

  // Create or update company settings
  ipcMain.handle('save-company-settings', async (_event, settingsData: Omit<CompanySettings, 'id' | 'create_dt' | 'update_dt'>): Promise<CompanySettingsResponse> => {
    let client: Client | null = null;

    try {
      console.log('💾 Saving company settings...');
      client = await getDbConnection();

      // Check if settings already exist
      const checkQuery = 'SELECT id FROM company_setting LIMIT 1';
      const checkResult = await client.query(checkQuery);

      let query: string;
      let values: any[];

      if (checkResult.rows.length === 0) {
        // Create new record
        console.log('➕ Creating new company settings record');
        query = `
          INSERT INTO company_setting (
            company_name_th, company_name_en, contact_name, tax_id, iin,
            address_th, address_en, telephone, fax, email, website,
            additional_info, bank_name, branch, rate, logo_path, active, create_by
          )
          VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18)
          RETURNING id, company_name_th, company_name_en, contact_name, tax_id, iin,
                    address_th, address_en, telephone, fax, email, website,
                    additional_info, bank_name, branch, rate, logo_path, active,
                    create_by, create_dt, update_by, update_dt
        `;

        values = [
          settingsData.company_name_th,
          settingsData.company_name_en,
          settingsData.contact_name,
          settingsData.tax_id || null,
          settingsData.iin || null,
          settingsData.address_th || null,
          settingsData.address_en || null,
          settingsData.telephone || null,
          settingsData.fax || null,
          settingsData.email || null,
          settingsData.website || null,
          settingsData.additional_info || null,
          settingsData.bank_name || null,
          settingsData.branch || null,
          settingsData.rate || null,
          settingsData.logo_path || null,
          settingsData.active,
          settingsData.create_by || 'SYSTEM'
        ];
      } else {
        // Update existing record
        const existingId = checkResult.rows[0].id;
        console.log('✏️ Updating existing company settings record ID:', existingId);
        
        query = `
          UPDATE company_setting
          SET company_name_th = $1, company_name_en = $2, contact_name = $3,
              tax_id = $4, iin = $5, address_th = $6, address_en = $7,
              telephone = $8, fax = $9, email = $10, website = $11,
              additional_info = $12, bank_name = $13, branch = $14, rate = $15,
              logo_path = $16, active = $17, update_by = $18, update_dt = CURRENT_TIMESTAMP
          WHERE id = $19
          RETURNING id, company_name_th, company_name_en, contact_name, tax_id, iin,
                    address_th, address_en, telephone, fax, email, website,
                    additional_info, bank_name, branch, rate, logo_path, active,
                    create_by, create_dt, update_by, update_dt
        `;

        values = [
          settingsData.company_name_th,
          settingsData.company_name_en,
          settingsData.contact_name,
          settingsData.tax_id || null,
          settingsData.iin || null,
          settingsData.address_th || null,
          settingsData.address_en || null,
          settingsData.telephone || null,
          settingsData.fax || null,
          settingsData.email || null,
          settingsData.website || null,
          settingsData.additional_info || null,
          settingsData.bank_name || null,
          settingsData.branch || null,
          settingsData.rate || null,
          settingsData.logo_path || null,
          settingsData.active,
          settingsData.update_by || 'SYSTEM',
          existingId
        ];
      }

      const result = await client.query(query, values);
      const savedSettings = result.rows[0];

      console.log('✅ Company settings saved successfully');
      return {
        success: true,
        message: 'Company settings saved successfully',
        data: savedSettings
      };

    } catch (error: any) {
      console.error('❌ Error saving company settings:', error.message);
      return {
        success: false,
        message: 'Failed to save company settings',
        error: error.message
      };
    } finally {
      if (client) {
        await client.end();
      }
    }
  });
}
