import { ipcMain } from 'electron';
import { Client } from 'pg';
import { getDbConnection } from '../db';

interface NetworkServiceConfig {
  id?: number;
  // International Consumer Card
  intl_consumer_dual_brand: number;
  intl_consumer_prepaid_pin: number;
  intl_consumer_credit_classic: number;
  intl_consumer_credit_platinum: number;
  intl_consumer_credit_diamond: number;

  // International Commercial Card
  intl_commercial_amount_fee: number;
  intl_commercial_amount_min: number;
  intl_commercial_te_merchants: number;

  // Thai Payment Network - POS
  thai_pos_government: number;
  thai_pos_non_government: number;
  thai_pos_non_government_max: number;

  // Thai Payment Network - E-Commerce
  thai_ecommerce_government: number;
  thai_ecommerce_non_government: number;
  thai_ecommerce_non_government_max: number;

  // Tax Configuration
  vat_percentage: number;
  withhold_tax_percentage: number;

  active: boolean;
  create_by: string;
  create_dt?: string;
  update_by?: string;
  update_dt?: string;
}

interface NetworkServiceResponse {
  success: boolean;
  message: string;
  data?: NetworkServiceConfig;
  error?: string;
}

export function setupNetworkServiceHandlers() {
  console.log('Setting up Network Service handlers...');

  // Initialize network_service table
  ipcMain.handle('init-network-service-table', async (): Promise<NetworkServiceResponse> => {
    let client: Client | null = null;

    try {
      console.log('🔧 Initializing network_service table...');
      client = await getDbConnection();

      const createTableQuery = `
        CREATE TABLE IF NOT EXISTS network_service (
          id SERIAL PRIMARY KEY,
          
          -- International Consumer Card
          intl_consumer_dual_brand DECIMAL(10,3) NOT NULL DEFAULT 0,
          intl_consumer_prepaid_pin DECIMAL(10,3) NOT NULL DEFAULT 0,
          intl_consumer_credit_classic DECIMAL(10,3) NOT NULL DEFAULT 0,
          intl_consumer_credit_platinum DECIMAL(10,3) NOT NULL DEFAULT 0,
          intl_consumer_credit_diamond DECIMAL(10,3) NOT NULL DEFAULT 0,
          
          -- International Commercial Card
          intl_commercial_amount_fee DECIMAL(10,3) NOT NULL DEFAULT 0,
          intl_commercial_amount_min DECIMAL(10,3) NOT NULL DEFAULT 0,
          intl_commercial_te_merchants DECIMAL(10,3) NOT NULL DEFAULT 0,
          
          -- Thai Payment Network - POS
          thai_pos_government DECIMAL(10,3) NOT NULL DEFAULT 0,
          thai_pos_non_government DECIMAL(10,3) NOT NULL DEFAULT 0,
          thai_pos_non_government_max DECIMAL(10,3) NOT NULL DEFAULT 0,
          
          -- Thai Payment Network - E-Commerce
          thai_ecommerce_government DECIMAL(10,3) NOT NULL DEFAULT 0,
          thai_ecommerce_non_government DECIMAL(10,3) NOT NULL DEFAULT 0,
          thai_ecommerce_non_government_max DECIMAL(10,3) NOT NULL DEFAULT 0,
          
          -- Tax Configuration
          vat_percentage DECIMAL(5,2) NOT NULL DEFAULT 0,
          withhold_tax_percentage DECIMAL(5,2) NOT NULL DEFAULT 0,
          
          -- Status
          active BOOLEAN NOT NULL DEFAULT TRUE,
          create_by VARCHAR(100) NOT NULL,
          create_dt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          update_by VARCHAR(100),
          update_dt TIMESTAMP
        );
      `;

      await client.query(createTableQuery);
      console.log('✅ Network service table initialized successfully');

      return {
        success: true,
        message: 'Network service table initialized successfully'
      };

    } catch (error: any) {
      console.error('❌ Error initializing network service table:', error.message);
      return {
        success: false,
        message: 'Failed to initialize network service table',
        error: error.message
      };
    } finally {
      if (client) {
        await client.end();
      }
    }
  });

  // Get network service configuration (should return single record)
  ipcMain.handle('get-network-service-config', async (): Promise<NetworkServiceResponse> => {
    let client: Client | null = null;

    try {
      console.log('📖 Fetching network service configuration...');
      client = await getDbConnection();

      const query = `
        SELECT id, intl_consumer_dual_brand, intl_consumer_prepaid_pin,
               intl_consumer_credit_classic, intl_consumer_credit_platinum,
               intl_consumer_credit_diamond, intl_commercial_amount_fee,
               intl_commercial_amount_min, intl_commercial_te_merchants,
               thai_pos_government, thai_pos_non_government, thai_pos_non_government_max,
               thai_ecommerce_government, thai_ecommerce_non_government,
               thai_ecommerce_non_government_max, vat_percentage, withhold_tax_percentage,
               active, create_by, create_dt, update_by, update_dt
        FROM network_service
        ORDER BY id DESC
        LIMIT 1
      `;

      const result = await client.query(query);

      if (result.rows.length === 0) {
        console.log('ℹ️ No network service configuration found');
        return {
          success: true,
          message: 'No network service configuration found',
          data: undefined
        };
      }

      console.log('✅ Network service configuration retrieved successfully');
      return {
        success: true,
        message: 'Network service configuration retrieved successfully',
        data: result.rows[0]
      };

    } catch (error: any) {
      console.error('❌ Error fetching network service configuration:', error.message);
      return {
        success: false,
        message: 'Failed to fetch network service configuration',
        error: error.message
      };
    } finally {
      if (client) {
        await client.end();
      }
    }
  });

  // Create or update network service configuration
  ipcMain.handle('save-network-service-config', async (_event, configData: Omit<NetworkServiceConfig, 'id' | 'create_dt' | 'update_dt'>): Promise<NetworkServiceResponse> => {
    let client: Client | null = null;

    try {
      console.log('💾 Saving network service configuration...');
      client = await getDbConnection();

      // Check if configuration already exists
      const checkQuery = 'SELECT id FROM network_service LIMIT 1';
      const checkResult = await client.query(checkQuery);

      let query: string;
      let values: any[];

      if (checkResult.rows.length === 0) {
        // Create new record
        console.log('➕ Creating new network service configuration record');
        query = `
          INSERT INTO network_service (
            intl_consumer_dual_brand, intl_consumer_prepaid_pin, intl_consumer_credit_classic,
            intl_consumer_credit_platinum, intl_consumer_credit_diamond, intl_commercial_amount_fee,
            intl_commercial_amount_min, intl_commercial_te_merchants, thai_pos_government,
            thai_pos_non_government, thai_pos_non_government_max, thai_ecommerce_government,
            thai_ecommerce_non_government, thai_ecommerce_non_government_max, vat_percentage,
            withhold_tax_percentage, active, create_by
          )
          VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18)
          RETURNING id, intl_consumer_dual_brand, intl_consumer_prepaid_pin, intl_consumer_credit_classic,
                    intl_consumer_credit_platinum, intl_consumer_credit_diamond, intl_commercial_amount_fee,
                    intl_commercial_amount_min, intl_commercial_te_merchants, thai_pos_government,
                    thai_pos_non_government, thai_pos_non_government_max, thai_ecommerce_government,
                    thai_ecommerce_non_government, thai_ecommerce_non_government_max, vat_percentage,
                    withhold_tax_percentage, active, create_by, create_dt, update_by, update_dt
        `;

        values = [
          configData.intl_consumer_dual_brand,
          configData.intl_consumer_prepaid_pin,
          configData.intl_consumer_credit_classic,
          configData.intl_consumer_credit_platinum,
          configData.intl_consumer_credit_diamond,
          configData.intl_commercial_amount_fee,
          configData.intl_commercial_amount_min,
          configData.intl_commercial_te_merchants,
          configData.thai_pos_government,
          configData.thai_pos_non_government,
          configData.thai_pos_non_government_max,
          configData.thai_ecommerce_government,
          configData.thai_ecommerce_non_government,
          configData.thai_ecommerce_non_government_max,
          configData.vat_percentage,
          configData.withhold_tax_percentage,
          configData.active,
          configData.create_by || 'SYSTEM'
        ];
      } else {
        // Update existing record
        const existingId = checkResult.rows[0].id;
        console.log('✏️ Updating existing network service configuration record ID:', existingId);
        
        query = `
          UPDATE network_service
          SET intl_consumer_dual_brand = $1, intl_consumer_prepaid_pin = $2,
              intl_consumer_credit_classic = $3, intl_consumer_credit_platinum = $4,
              intl_consumer_credit_diamond = $5, intl_commercial_amount_fee = $6,
              intl_commercial_amount_min = $7, intl_commercial_te_merchants = $8,
              thai_pos_government = $9, thai_pos_non_government = $10,
              thai_pos_non_government_max = $11, thai_ecommerce_government = $12,
              thai_ecommerce_non_government = $13, thai_ecommerce_non_government_max = $14,
              vat_percentage = $15, withhold_tax_percentage = $16, active = $17,
              update_by = $18, update_dt = CURRENT_TIMESTAMP
          WHERE id = $19
          RETURNING id, intl_consumer_dual_brand, intl_consumer_prepaid_pin, intl_consumer_credit_classic,
                    intl_consumer_credit_platinum, intl_consumer_credit_diamond, intl_commercial_amount_fee,
                    intl_commercial_amount_min, intl_commercial_te_merchants, thai_pos_government,
                    thai_pos_non_government, thai_pos_non_government_max, thai_ecommerce_government,
                    thai_ecommerce_non_government, thai_ecommerce_non_government_max, vat_percentage,
                    withhold_tax_percentage, active, create_by, create_dt, update_by, update_dt
        `;

        values = [
          configData.intl_consumer_dual_brand,
          configData.intl_consumer_prepaid_pin,
          configData.intl_consumer_credit_classic,
          configData.intl_consumer_credit_platinum,
          configData.intl_consumer_credit_diamond,
          configData.intl_commercial_amount_fee,
          configData.intl_commercial_amount_min,
          configData.intl_commercial_te_merchants,
          configData.thai_pos_government,
          configData.thai_pos_non_government,
          configData.thai_pos_non_government_max,
          configData.thai_ecommerce_government,
          configData.thai_ecommerce_non_government,
          configData.thai_ecommerce_non_government_max,
          configData.vat_percentage,
          configData.withhold_tax_percentage,
          configData.active,
          configData.update_by || 'SYSTEM',
          existingId
        ];
      }

      const result = await client.query(query, values);
      const savedConfig = result.rows[0];

      console.log('✅ Network service configuration saved successfully');
      return {
        success: true,
        message: 'Network service configuration saved successfully',
        data: savedConfig
      };

    } catch (error: any) {
      console.error('❌ Error saving network service configuration:', error.message);
      return {
        success: false,
        message: 'Failed to save network service configuration',
        error: error.message
      };
    } finally {
      if (client) {
        await client.end();
      }
    }
  });
}
