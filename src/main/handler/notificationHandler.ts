import { ipcMain, dialog, Notification, BrowserWindow, nativeImage } from 'electron';
import path from 'path';
import { customIcon } from '../index';

// Interface for notification options
interface NotificationOptions {
  title: string;
  body: string;
  icon?: string;
  silent?: boolean;
  urgency?: 'normal' | 'critical' | 'low';
}

// Interface for dialog options
interface DialogOptions {
  type?: 'none' | 'info' | 'error' | 'question' | 'warning';
  title?: string;
  message: string;
  detail?: string;
  buttons?: string[];
  defaultId?: number;
  cancelId?: number;
}

export function setupNotificationHandlers() {
  console.log('Setting up notification and dialog handlers...');

  // System notification handler
  ipcMain.handle('show-system-notification', async (_event, options: NotificationOptions) => {
    try {
      console.log('📢 Showing system notification:', options.title);

      // Check if notifications are supported
      if (!Notification.isSupported()) {
        console.warn('⚠️ System notifications are not supported on this platform');
        return {
          success: false,
          error: 'System notifications are not supported'
        };
      }

      // Create notification with custom icon
      const notification = new Notification({
        title: options.title,
        body: options.body,
        icon: customIcon || undefined, // Use custom icon if available
        silent: options.silent || false,
        urgency: options.urgency || 'normal'
      });

      // Show the notification
      notification.show();

      // Handle notification events
      notification.on('click', () => {
        console.log('📢 Notification clicked');
        // Bring the main window to focus when notification is clicked
        const mainWindow = BrowserWindow.getAllWindows()[0];
        if (mainWindow) {
          if (mainWindow.isMinimized()) mainWindow.restore();
          mainWindow.focus();
        }
      });

      notification.on('close', () => {
        console.log('📢 Notification closed');
      });

      return {
        success: true,
        message: 'Notification shown successfully'
      };
    } catch (error: any) {
      console.error('❌ Error showing system notification:', error.message);
      return {
        success: false,
        error: error.message
      };
    }
  });

  // Custom dialog handler with icon
  ipcMain.handle('show-custom-dialog', async (_event, options: DialogOptions) => {
    try {
      console.log('💬 Showing custom dialog:', options.title || options.message);

      const mainWindow = BrowserWindow.getAllWindows()[0];
      
      const dialogOptions: any = {
        type: options.type || 'info',
        title: options.title || 'Eposservice',
        message: options.message,
        detail: options.detail,
        buttons: options.buttons || ['OK'],
        defaultId: options.defaultId || 0,
        cancelId: options.cancelId,
        icon: customIcon || undefined // Use custom icon if available
      };

      let result;
      if (mainWindow) {
        result = await dialog.showMessageBox(mainWindow, dialogOptions);
      } else {
        result = await dialog.showMessageBox(dialogOptions);
      }

      console.log('💬 Dialog result:', result);
      return {
        success: true,
        response: result.response,
        checkboxChecked: result.checkboxChecked
      };
    } catch (error: any) {
      console.error('❌ Error showing custom dialog:', error.message);
      return {
        success: false,
        error: error.message
      };
    }
  });

  // Confirmation dialog handler
  ipcMain.handle('show-confirmation-dialog', async (_event, message: string, title?: string) => {
    try {
      console.log('❓ Showing confirmation dialog:', title || message);

      const mainWindow = BrowserWindow.getAllWindows()[0];
      
      const dialogOptions: any = {
        type: 'question',
        title: title || 'Confirm Action',
        message: message,
        buttons: ['Cancel', 'OK'],
        defaultId: 1,
        cancelId: 0,
        icon: customIcon || undefined
      };

      let result;
      if (mainWindow) {
        result = await dialog.showMessageBox(mainWindow, dialogOptions);
      } else {
        result = await dialog.showMessageBox(dialogOptions);
      }

      return {
        success: true,
        confirmed: result.response === 1
      };
    } catch (error: any) {
      console.error('❌ Error showing confirmation dialog:', error.message);
      return {
        success: false,
        error: error.message
      };
    }
  });

  // Error dialog handler
  ipcMain.handle('show-error-dialog', async (_event, message: string, title?: string, detail?: string) => {
    try {
      console.log('❌ Showing error dialog:', title || message);

      const mainWindow = BrowserWindow.getAllWindows()[0];
      
      const dialogOptions: any = {
        type: 'error',
        title: title || 'Error',
        message: message,
        detail: detail,
        buttons: ['OK'],
        defaultId: 0,
        icon: customIcon || undefined
      };

      let result;
      if (mainWindow) {
        result = await dialog.showMessageBox(mainWindow, dialogOptions);
      } else {
        result = await dialog.showMessageBox(dialogOptions);
      }

      return {
        success: true,
        response: result.response
      };
    } catch (error: any) {
      console.error('❌ Error showing error dialog:', error.message);
      return {
        success: false,
        error: error.message
      };
    }
  });

  // Warning dialog handler
  ipcMain.handle('show-warning-dialog', async (_event, message: string, title?: string, detail?: string) => {
    try {
      console.log('⚠️ Showing warning dialog:', title || message);

      const mainWindow = BrowserWindow.getAllWindows()[0];
      
      const dialogOptions: any = {
        type: 'warning',
        title: title || 'Warning',
        message: message,
        detail: detail,
        buttons: ['OK'],
        defaultId: 0,
        icon: customIcon || undefined
      };

      let result;
      if (mainWindow) {
        result = await dialog.showMessageBox(mainWindow, dialogOptions);
      } else {
        result = await dialog.showMessageBox(dialogOptions);
      }

      return {
        success: true,
        response: result.response
      };
    } catch (error: any) {
      console.error('❌ Error showing warning dialog:', error.message);
      return {
        success: false,
        error: error.message
      };
    }
  });

  // Info dialog handler
  ipcMain.handle('show-info-dialog', async (_event, message: string, title?: string, detail?: string) => {
    try {
      console.log('ℹ️ Showing info dialog:', title || message);

      const mainWindow = BrowserWindow.getAllWindows()[0];
      
      const dialogOptions: any = {
        type: 'info',
        title: title || 'Information',
        message: message,
        detail: detail,
        buttons: ['OK'],
        defaultId: 0,
        icon: customIcon || undefined
      };

      let result;
      if (mainWindow) {
        result = await dialog.showMessageBox(mainWindow, dialogOptions);
      } else {
        result = await dialog.showMessageBox(dialogOptions);
      }

      return {
        success: true,
        response: result.response
      };
    } catch (error: any) {
      console.error('❌ Error showing info dialog:', error.message);
      return {
        success: false,
        error: error.message
      };
    }
  });
}
