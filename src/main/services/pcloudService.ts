import axios from 'axios';
import fs from 'fs';
import FormData from 'form-data';
import path from 'path';
import { getPCloudConfig } from '../config/env';

interface PCloudConfig {
  username: string;
  password: string;
  region?: 'us' | 'eu';
}

interface PCloudAuthResult {
  success: boolean;
  auth?: string;
  error?: string;
}

interface PCloudUploadResult {
  success: boolean;
  fileId?: number;
  fileName?: string;
  size?: number;
  remotePath?: string;
  error?: string;
}

interface PCloudFolderResult {
  success: boolean;
  folderId?: number;
  folderPath?: string;
  error?: string;
}

export class PCloudService {
  private config: PCloudConfig;
  private apiHost: string;
  private authToken: string | null = null;

  constructor(config: PCloudConfig) {
    this.config = config;
    this.apiHost = config.region === 'eu' ? 'eapi.pcloud.com' : 'api.pcloud.com';
  }

  /**
   * Authenticate with pCloud and get auth token
   */
  async authenticate(): Promise<PCloudAuthResult> {
    try {
      console.log('🔐 Authenticating with pCloud...');
      
      const authParams = new URLSearchParams({
        username: this.config.username,
        password: this.config.password,
        getauth: '1',
        logout: '1'
      });

      const envConfig = getPCloudConfig();
      const response = await axios.get(`https://${this.apiHost}/userinfo?${authParams.toString()}`, {
        timeout: envConfig.timeout,
        headers: {
          'User-Agent': 'Electron-CSV-Backup/1.0'
        }
      });

      if (response.data.result === 0) {
        this.authToken = response.data.auth;
        console.log('✅ pCloud authentication successful');
        return { success: true, auth: this.authToken || undefined };
      } else {
        const errorMessage = this.getErrorMessage(response.data.result);
        console.error('❌ pCloud authentication failed:', errorMessage);
        return { success: false, error: errorMessage };
      }
    } catch (error: any) {
      console.error('❌ pCloud authentication error:', error.message);
      return { success: false, error: error.message };
    }
  }

  /**
   * Create folder in pCloud if it doesn't exist
   */
  async createFolder(folderPath: string): Promise<PCloudFolderResult> {
    try {
      if (!this.authToken) {
        const authResult = await this.authenticate();
        if (!authResult.success) {
          return { success: false, error: authResult.error };
        }
      }

      console.log(`📁 Creating/checking pCloud folder: ${folderPath}`);

      // Check if folder exists first
      const listParams = new URLSearchParams({
        auth: this.authToken!,
        path: folderPath
      });

      try {
        const listResponse = await axios.get(`https://${this.apiHost}/listfolder?${listParams.toString()}`);
        if (listResponse.data.result === 0) {
          console.log(`✅ Folder already exists: ${folderPath}`);
          return {
            success: true,
            folderId: listResponse.data.metadata.folderid,
            folderPath
          };
        }
      } catch (error) {
        // Folder doesn't exist, continue to create it
      }

      // Create parent folders first if needed
      const pathParts = folderPath.split('/').filter(part => part.length > 0);
      let currentPath = '';

      for (const part of pathParts) {
        currentPath += '/' + part;

        // Check if this level exists
        const checkParams = new URLSearchParams({
          auth: this.authToken!,
          path: currentPath
        });

        try {
          const checkResponse = await axios.get(`https://${this.apiHost}/listfolder?${checkParams.toString()}`);
          if (checkResponse.data.result === 0) {
            console.log(`✅ Path exists: ${currentPath}`);
            continue; // This level exists, continue to next
          }
        } catch (error) {
          // Path doesn't exist, need to create it
        }

        // Create this level
        const createParams = new URLSearchParams({
          auth: this.authToken!,
          path: currentPath
        });

        console.log(`📁 Creating folder level: ${currentPath}`);
        const createResponse = await axios.get(`https://${this.apiHost}/createfolder?${createParams.toString()}`);

        if (createResponse.data.result === 0) {
          console.log(`✅ Created folder level: ${currentPath}`);
        } else if (createResponse.data.result === 2002) {
          console.log(`✅ Folder level already exists: ${currentPath}`);
        } else {
          const errorMessage = this.getErrorMessage(createResponse.data.result);
          console.error(`❌ Failed to create folder level ${currentPath}: ${errorMessage}`);
          return { success: false, error: `Failed to create ${currentPath}: ${errorMessage}` };
        }
      }

      // Final check to get folder info
      try {
        const finalListResponse = await axios.get(`https://${this.apiHost}/listfolder?${listParams.toString()}`);
        if (finalListResponse.data.result === 0) {
          return {
            success: true,
            folderId: finalListResponse.data.metadata.folderid,
            folderPath
          };
        }
      } catch (error) {
        // If we can't get folder info, still consider it a success since we created it
      }

      return { success: true, folderPath };
    } catch (error: any) {
      console.error('❌ Error creating pCloud folder:', error.message);
      return { success: false, error: error.message };
    }
  }

  /**
   * Upload file to pCloud with date-based folder structure
   */
  async uploadFileWithDateFolder(
    localFilePath: string,
    baseFolder: string = '/csv_backup',
    date?: Date
  ): Promise<PCloudUploadResult> {
    const currentDate = date || new Date();
    const dateFolder = currentDate.toISOString().split('T')[0]; // Format: YYYY-MM-DD
    const remoteFolderPath = `${baseFolder}/${dateFolder}`;

    console.log(`📅 Uploading to date-based folder: ${remoteFolderPath}`);

    return this.uploadFile(localFilePath, remoteFolderPath);
  }

  /**
   * Upload file to pCloud
   */
  async uploadFile(localFilePath: string, remoteFolder: string = '/CSV_Backups'): Promise<PCloudUploadResult> {
    try {
      if (!this.authToken) {
        const authResult = await this.authenticate();
        if (!authResult.success) {
          return { success: false, error: authResult.error };
        }
      }

      if (!fs.existsSync(localFilePath)) {
        return { success: false, error: 'Local file does not exist' };
      }

      // Ensure remote folder exists
      const folderResult = await this.createFolder(remoteFolder);
      if (!folderResult.success) {
        return { success: false, error: `Failed to create remote folder: ${folderResult.error}` };
      }

      const fileName = path.basename(localFilePath);
      const fileStats = fs.statSync(localFilePath);
      
      console.log(`☁️ Uploading file to pCloud: ${fileName} (${fileStats.size} bytes)`);

      // Create form data for file upload
      const formData = new FormData();
      formData.append('auth', this.authToken!);
      formData.append('path', remoteFolder);
      formData.append('filename', fs.createReadStream(localFilePath));

      const envConfig = getPCloudConfig();
      const uploadResponse = await axios.post(`https://${this.apiHost}/uploadfile`, formData, {
        headers: {
          ...formData.getHeaders(),
          'User-Agent': 'Electron-CSV-Backup/1.0'
        },
        timeout: envConfig.uploadTimeout,
        maxContentLength: Infinity,
        maxBodyLength: Infinity
      });

      if (uploadResponse.data.result === 0) {
        const uploadedFile = uploadResponse.data.metadata[0];
        console.log(`✅ File uploaded successfully: ${fileName}`);
        console.log(`📍 Remote path: ${remoteFolder}/${fileName}`);
        console.log(`🆔 File ID: ${uploadedFile.fileid}`);

        return {
          success: true,
          fileId: uploadedFile.fileid,
          fileName: uploadedFile.name,
          size: uploadedFile.size,
          remotePath: `${remoteFolder}/${fileName}`
        };
      } else {
        const errorMessage = this.getErrorMessage(uploadResponse.data.result);
        console.error('❌ pCloud upload failed:', errorMessage);
        return { success: false, error: errorMessage };
      }
    } catch (error: any) {
      console.error('❌ Error uploading file to pCloud:', error.message);
      return { success: false, error: error.message };
    }
  }

  /**
   * Upload multiple files to pCloud with date-based folders
   */
  async uploadFilesWithDateFolders(
    localFilePaths: string[],
    baseFolder: string = '/csv_backup',
    date?: Date
  ): Promise<{
    success: boolean;
    results: PCloudUploadResult[];
    successCount: number;
    failureCount: number;
  }> {
    const currentDate = date || new Date();
    const dateFolder = currentDate.toISOString().split('T')[0]; // Format: YYYY-MM-DD
    const remoteFolderPath = `${baseFolder}/${dateFolder}`;

    console.log(`📅 Batch uploading to date-based folder: ${remoteFolderPath}`);

    return this.uploadFiles(localFilePaths, remoteFolderPath);
  }

  /**
   * Upload multiple files to pCloud
   */
  async uploadFiles(localFilePaths: string[], remoteFolder: string = '/CSV_Backups'): Promise<{
    success: boolean;
    results: PCloudUploadResult[];
    successCount: number;
    failureCount: number;
  }> {
    const results: PCloudUploadResult[] = [];
    let successCount = 0;
    let failureCount = 0;

    console.log(`☁️ Starting batch upload of ${localFilePaths.length} files to pCloud`);

    for (const filePath of localFilePaths) {
      const result = await this.uploadFile(filePath, remoteFolder);
      results.push(result);
      
      if (result.success) {
        successCount++;
      } else {
        failureCount++;
      }

      // Small delay between uploads to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    console.log(`📊 Batch upload completed: ${successCount} success, ${failureCount} failed`);

    return {
      success: failureCount === 0,
      results,
      successCount,
      failureCount
    };
  }

  /**
   * Get error message from pCloud API result code
   */
  private getErrorMessage(resultCode: number): string {
    switch (resultCode) {
      case 1000:
        return 'Login required - invalid credentials';
      case 2000:
        return 'Login failed - check username and password';
      case 2001:
        return 'Invalid parameters';
      case 2002:
        return 'Directory already exists';
      case 2003:
        return 'Access denied';
      case 2004:
        return 'Directory name too long';
      case 2005:
        return 'Directory does not exist';
      case 2006:
        return 'Cannot create directory';
      case 2010:
        return 'Invalid path';
      case 4000:
        return 'Too many login attempts from this IP address';
      case 5001:
        return 'File already exists';
      case 5004:
        return 'No space left on account';
      default:
        return `API error: ${resultCode}`;
    }
  }

  /**
   * Test connection to pCloud
   */
  async testConnection(): Promise<{ success: boolean; message: string; userInfo?: any }> {
    try {
      const authResult = await this.authenticate();
      if (!authResult.success) {
        return { success: false, message: authResult.error || 'Authentication failed' };
      }

      // Get user info to verify connection
      const userInfoParams = new URLSearchParams({
        auth: this.authToken!
      });

      const response = await axios.get(`https://${this.apiHost}/userinfo?${userInfoParams.toString()}`);
      
      if (response.data.result === 0) {
        return {
          success: true,
          message: 'pCloud connection successful',
          userInfo: {
            email: response.data.email,
            premium: response.data.premium,
            quota: response.data.quota,
            usedQuota: response.data.usedquota
          }
        };
      } else {
        return { success: false, message: this.getErrorMessage(response.data.result) };
      }
    } catch (error: any) {
      return { success: false, message: error.message };
    }
  }
}
