import React from 'react';
import { useRoleAccess } from '../hooks/useRoleAccess';

/**
 * Component to display a comprehensive summary of role-based access control
 */
export function RoleAccessSummary() {
  const {
    userRole,
    isAdmin,
    isMaker,
    isViewer,
    canCreate,
    canUpdate,
    canDelete,
    canView,
    canSearch,
    canFilter,
    canExport,
    hasWriteAccess,
    hasReadOnlyAccess
  } = useRoleAccess();

  const getRoleColor = () => {
    if (isAdmin) return 'bg-purple-100 text-purple-800 border-purple-200';
    if (isMaker) return 'bg-blue-100 text-blue-800 border-blue-200';
    if (isViewer) return 'bg-green-100 text-green-800 border-green-200';
    return 'bg-gray-100 text-gray-800 border-gray-200';
  };

  const permissions = [
    { name: 'View Data', allowed: canView, icon: '👁️' },
    { name: 'Search & Filter', allowed: canSearch && canFilter, icon: '🔍' },
    { name: 'Export Data', allowed: canExport, icon: '📤' },
    { name: 'Create Records', allowed: canCreate, icon: '➕' },
    { name: 'Update Records', allowed: canUpdate, icon: '✏️' },
    { name: 'Delete Records', allowed: canDelete, icon: '🗑️' },
  ];

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">Access Control Summary</h3>
        <span className={`px-3 py-1 rounded-full text-sm font-medium border ${getRoleColor()}`}>
          {userRole?.toUpperCase() || 'UNKNOWN'}
        </span>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        <div>
          <h4 className="font-medium text-gray-700 mb-2">Role Information</h4>
          <div className="space-y-1 text-sm">
            <p><span className="font-medium">Current Role:</span> {userRole?.toUpperCase() || 'Unknown'}</p>
            <p><span className="font-medium">Access Level:</span> {hasWriteAccess ? 'Read/Write' : 'Read Only'}</p>
            <p><span className="font-medium">Admin Status:</span> {isAdmin ? 'Yes' : 'No'}</p>
          </div>
        </div>

        <div>
          <h4 className="font-medium text-gray-700 mb-2">Master Data Sections</h4>
          <div className="space-y-1 text-sm">
            <p><span className="font-medium">Bank Master:</span> {hasWriteAccess ? 'Full Access' : 'View Only'}</p>
            <p><span className="font-medium">Merchant Management:</span> {hasWriteAccess ? 'Full Access' : 'View Only'}</p>
            <p><span className="font-medium">Network Service:</span> {hasWriteAccess ? 'Full Access' : 'View Only'}</p>
            <p><span className="font-medium">Company Settings:</span> {isAdmin ? 'Full Access' : 'View Only'}</p>
            <p><span className="font-medium">User Management:</span> {isAdmin ? 'Full Access' : 'No Access'}</p>
          </div>
        </div>
      </div>

      <div>
        <h4 className="font-medium text-gray-700 mb-3">Permissions</h4>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
          {permissions.map((permission) => (
            <div
              key={permission.name}
              className={`flex items-center gap-2 p-2 rounded-md text-sm ${
                permission.allowed
                  ? 'bg-green-50 text-green-700 border border-green-200'
                  : 'bg-red-50 text-red-700 border border-red-200'
              }`}
            >
              <span className="text-base">{permission.icon}</span>
              <span className="font-medium">{permission.name}</span>
              <span className="ml-auto">
                {permission.allowed ? '✅' : '❌'}
              </span>
            </div>
          ))}
        </div>
      </div>

      <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
        <h5 className="font-medium text-blue-800 mb-1">Role Descriptions</h5>
        <div className="text-sm text-blue-700 space-y-1">
          <p><span className="font-medium">Admin:</span> Full access to all features including user management and company settings</p>
          <p><span className="font-medium">Maker:</span> Can create, update, and delete master data records</p>
          <p><span className="font-medium">View:</span> Can only view, search, filter, and export data</p>
        </div>
      </div>
    </div>
  );
}

/**
 * Compact version for headers
 */
export function RoleAccessBadge() {
  const { userRole, hasWriteAccess, isAdmin } = useRoleAccess();

  const getBadgeColor = () => {
    if (isAdmin) return 'bg-purple-100 text-purple-800';
    if (hasWriteAccess) return 'bg-blue-100 text-blue-800';
    return 'bg-green-100 text-green-800';
  };

  return (
    <div className="flex items-center gap-2 text-sm">
      <span className={`px-2 py-1 rounded-full font-medium ${getBadgeColor()}`}>
        {userRole?.toUpperCase() || 'UNKNOWN'}
      </span>
      <span className="text-gray-600">
        {isAdmin ? 'Admin Access' : hasWriteAccess ? 'Read/Write' : 'Read Only'}
      </span>
    </div>
  );
}
