import React from 'react';
import { useRoleAccess } from '../hooks/useRoleAccess';

/**
 * Component wrapper for role-based rendering
 */
interface RoleBasedComponentProps {
  children: React.ReactNode;
  requiredPermission: keyof ReturnType<typeof useRoleAccess>;
  fallback?: React.ReactNode;
}

export function RoleBasedComponent({ 
  children, 
  requiredPermission, 
  fallback = null 
}: RoleBasedComponentProps) {
  const roleAccess = useRoleAccess();
  
  if (roleAccess[requiredPermission]) {
    return <>{children}</>;
  }
  
  return <>{fallback}</>;
}
