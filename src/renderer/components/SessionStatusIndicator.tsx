import React from 'react';
import { useAuth } from '../contexts/AuthContext';
import { useSessionTimeRemaining } from './SessionTimeoutWarning';

interface SessionStatusIndicatorProps {
  className?: string;
  showTimeRemaining?: boolean;
  compact?: boolean;
}

export function SessionStatusIndicator({ 
  className = '', 
  showTimeRemaining = true,
  compact = false 
}: SessionStatusIndicatorProps) {
  const { isAuthenticated, user } = useAuth();
  const timeRemaining = useSessionTimeRemaining();

  if (!isAuthenticated || !user) {
    return null;
  }

  const formatTimeRemaining = (milliseconds: number): string => {
    // Handle expired sessions
    if (milliseconds <= 0) {
      return 'EXPIRED';
    }

    const totalMinutes = Math.floor(milliseconds / (1000 * 60));
    const hours = Math.floor(totalMinutes / 60);
    const minutes = totalMinutes % 60;

    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    }
    return `${minutes}m`;
  };

  const getStatusColor = (milliseconds: number | null): string => {
    if (!milliseconds || milliseconds <= 0) return 'text-red-500'; // Expired or no time

    const minutes = Math.floor(milliseconds / (1000 * 60));
    if (minutes <= 5) return 'text-red-500';
    if (minutes <= 15) return 'text-yellow-500';
    return 'text-green-500';
  };

  const getStatusIcon = (milliseconds: number | null) => {
    const colorClass = getStatusColor(milliseconds);

    // Show pulsing red dot for expired sessions
    if (!milliseconds || milliseconds <= 0) {
      return (
        <div className="relative">
          <div className="w-2 h-2 rounded-full bg-red-500 animate-pulse" />
          <div className="absolute inset-0 w-2 h-2 rounded-full bg-red-500 animate-ping opacity-75" />
        </div>
      );
    }

    return (
      <div className={`w-2 h-2 rounded-full ${colorClass.replace('text-', 'bg-')}`} />
    );
  };

  if (compact) {
    return (
      <div className={`flex items-center space-x-2 ${className}`}>
        {getStatusIcon(timeRemaining)}
        {showTimeRemaining && timeRemaining && (
          <span className={`text-xs ${getStatusColor(timeRemaining)}`}>
            {formatTimeRemaining(timeRemaining)}
          </span>
        )}
      </div>
    );
  }

  return (
    <div className={`flex items-center space-x-3 ${className}`}>
      <div className="flex items-center space-x-2">
        {getStatusIcon(timeRemaining)}
        <div className="text-sm">
          <div className="font-medium text-gray-900">{user.user_name}</div>
          {user.role_name && (
            <div className="text-xs text-gray-500">{user.role_name}</div>
          )}
        </div>
      </div>
      
      {showTimeRemaining && timeRemaining && (
        <div className="text-xs">
          <div className="text-gray-500">Session expires in</div>
          <div className={`font-medium ${getStatusColor(timeRemaining)}`}>
            {formatTimeRemaining(timeRemaining)}
          </div>
        </div>
      )}
    </div>
  );
}

// Simple session timer component for minimal display
export function SessionTimer({ className = '' }: { className?: string }) {
  const timeRemaining = useSessionTimeRemaining();

  if (!timeRemaining) {
    return null;
  }

  const formatTime = (milliseconds: number): string => {
    // Handle expired sessions
    if (milliseconds <= 0) {
      return 'EXPIRED';
    }

    const minutes = Math.floor(milliseconds / (1000 * 60));
    const seconds = Math.floor((milliseconds % (1000 * 60)) / 1000);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const getColorClass = (milliseconds: number): string => {
    if (milliseconds <= 0) return 'text-red-600 bg-red-50 border-red-200 animate-pulse';

    const minutes = Math.floor(milliseconds / (1000 * 60));
    if (minutes <= 5) return 'text-red-600 bg-red-50 border-red-200';
    if (minutes <= 15) return 'text-yellow-600 bg-yellow-50 border-yellow-200';
    return 'text-green-600 bg-green-50 border-green-200';
  };

  return (
    <div className={`inline-flex items-center px-2 py-1 rounded-md text-xs font-medium border ${getColorClass(timeRemaining)} ${className}`}>
      <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
      </svg>
      {formatTime(timeRemaining)}
    </div>
  );
}

// Session status badge for toolbar/header
export function SessionStatusBadge({ className = '' }: { className?: string }) {
  const { isAuthenticated } = useAuth();
  const timeRemaining = useSessionTimeRemaining();

  if (!isAuthenticated) {
    return (
      <div className={`inline-flex items-center px-2 py-1 rounded-md text-xs font-medium text-gray-600 bg-gray-100 border border-gray-200 ${className}`}>
        <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" clipRule="evenodd" />
        </svg>
        Not Connected
      </div>
    );
  }

  if (!timeRemaining) {
    return (
      <div className={`inline-flex items-center px-2 py-1 rounded-md text-xs font-medium text-blue-600 bg-blue-50 border border-blue-200 ${className}`}>
        <div className="w-2 h-2 bg-blue-500 rounded-full mr-2"></div>
        Connected
      </div>
    );
  }

  // Handle expired sessions
  if (timeRemaining <= 0) {
    return (
      <div className={`inline-flex items-center px-2 py-1 rounded-md text-xs font-medium text-red-600 bg-red-50 border border-red-200 animate-pulse ${className}`}>
        <div className="w-2 h-2 bg-red-500 rounded-full mr-2 animate-ping"></div>
        Session Expired
      </div>
    );
  }

  const minutes = Math.floor(timeRemaining / (1000 * 60));
  let statusClass = 'text-green-600 bg-green-50 border-green-200';
  let statusText = 'Active';

  if (minutes <= 5) {
    statusClass = 'text-red-600 bg-red-50 border-red-200';
    statusText = 'Expiring Soon';
  } else if (minutes <= 15) {
    statusClass = 'text-yellow-600 bg-yellow-50 border-yellow-200';
    statusText = 'Active';
  }

  return (
    <div className={`inline-flex items-center px-2 py-1 rounded-md text-xs font-medium border ${statusClass} ${className}`}>
      <div className={`w-2 h-2 rounded-full mr-2 ${statusClass.includes('green') ? 'bg-green-500' : statusClass.includes('yellow') ? 'bg-yellow-500' : 'bg-red-500'}`}></div>
      {statusText}
    </div>
  );
}
