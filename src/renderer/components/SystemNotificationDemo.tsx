import React, { useState } from 'react';
import {
  showSystemNotification,
  showCustomDialog,
  showConfirmationDialog,
  showErrorDialog,
  showWarningDialog,
  showInfoDialog,
  createSystemTray,
  destroySystemTray,
  updateTrayTooltip,
  showTrayBalloon,
  showSuccessNotification,
  showErrorNotification,
  showWarningNotification,
  showInfoNotification,
  showDeleteConfirmation,
  showSaveConfirmation,
  showExitConfirmation
} from '../utils/systemNotifications';

export const SystemNotificationDemo: React.FC = () => {
  const [trayCreated, setTrayCreated] = useState(false);
  const [notificationTitle, setNotificationTitle] = useState('Test Notification');
  const [notificationBody, setNotificationBody] = useState('This is a test notification with custom icon');
  const [dialogMessage, setDialogMessage] = useState('This is a test dialog with custom icon');

  const handleSystemNotification = async () => {
    const success = await showSystemNotification({
      title: notificationTitle,
      body: notificationBody,
      urgency: 'normal'
    });
    console.log('System notification result:', success);
  };

  const handleCustomDialog = async () => {
    const result = await showCustomDialog({
      type: 'info',
      title: 'Custom Dialog',
      message: dialogMessage,
      buttons: ['Cancel', 'OK'],
      defaultId: 1,
      cancelId: 0
    });
    console.log('Custom dialog result:', result);
  };

  const handleConfirmationDialog = async () => {
    const confirmed = await showConfirmationDialog(
      'Do you want to proceed with this action?',
      'Confirm Action'
    );
    console.log('Confirmation result:', confirmed);
  };

  const handleErrorDialog = async () => {
    const success = await showErrorDialog(
      'An error occurred while processing your request',
      'Error',
      'Please check your input and try again'
    );
    console.log('Error dialog result:', success);
  };

  const handleWarningDialog = async () => {
    const success = await showWarningDialog(
      'This action cannot be undone',
      'Warning',
      'Please make sure you want to continue'
    );
    console.log('Warning dialog result:', success);
  };

  const handleInfoDialog = async () => {
    const success = await showInfoDialog(
      'Your operation completed successfully',
      'Information',
      'All data has been saved'
    );
    console.log('Info dialog result:', success);
  };

  const handleCreateTray = async () => {
    const success = await createSystemTray();
    setTrayCreated(success);
    console.log('Create tray result:', success);
  };

  const handleDestroyTray = async () => {
    const success = await destroySystemTray();
    setTrayCreated(!success);
    console.log('Destroy tray result:', success);
  };

  const handleUpdateTrayTooltip = async () => {
    const success = await updateTrayTooltip('Updated tooltip - ' + new Date().toLocaleTimeString());
    console.log('Update tray tooltip result:', success);
  };

  const handleTrayBalloon = async () => {
    const success = await showTrayBalloon(
      'Tray Notification',
      'This is a balloon notification from the system tray'
    );
    console.log('Tray balloon result:', success);
  };

  const handleSuccessNotification = () => {
    showSuccessNotification('Operation completed successfully!');
  };

  const handleErrorNotification = () => {
    showErrorNotification('Something went wrong!');
  };

  const handleWarningNotification = () => {
    showWarningNotification('Please be careful with this action');
  };

  const handleInfoNotification = () => {
    showInfoNotification('Here is some useful information');
  };

  const handleDeleteConfirmation = async () => {
    const confirmed = await showDeleteConfirmation('Test Item');
    console.log('Delete confirmation result:', confirmed);
  };

  const handleSaveConfirmation = async () => {
    const confirmed = await showSaveConfirmation();
    console.log('Save confirmation result:', confirmed);
  };

  const handleExitConfirmation = async () => {
    const confirmed = await showExitConfirmation();
    console.log('Exit confirmation result:', confirmed);
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h2 className="text-2xl font-bold mb-6">System Notifications & Dialogs Demo</h2>
      <p className="text-gray-600 mb-6">
        This demo shows how to use custom system notifications and dialogs with your Epos logo.
        All notifications and dialogs will display the custom Epos.png icon.
      </p>

      {/* System Notifications Section */}
      <div className="mb-8">
        <h3 className="text-xl font-semibold mb-4">System Notifications</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <label className="block text-sm font-medium mb-2">Notification Title</label>
            <input
              type="text"
              value={notificationTitle}
              onChange={(e) => setNotificationTitle(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-2">Notification Body</label>
            <input
              type="text"
              value={notificationBody}
              onChange={(e) => setNotificationBody(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
            />
          </div>
        </div>
        <div className="flex flex-wrap gap-2 mb-4">
          <button
            onClick={handleSystemNotification}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Custom Notification
          </button>
          <button
            onClick={handleSuccessNotification}
            className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
          >
            Success Notification
          </button>
          <button
            onClick={handleErrorNotification}
            className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
          >
            Error Notification
          </button>
          <button
            onClick={handleWarningNotification}
            className="px-4 py-2 bg-yellow-500 text-white rounded hover:bg-yellow-600"
          >
            Warning Notification
          </button>
          <button
            onClick={handleInfoNotification}
            className="px-4 py-2 bg-blue-400 text-white rounded hover:bg-blue-500"
          >
            Info Notification
          </button>
        </div>
      </div>

      {/* System Dialogs Section */}
      <div className="mb-8">
        <h3 className="text-xl font-semibold mb-4">System Dialogs</h3>
        <div className="mb-4">
          <label className="block text-sm font-medium mb-2">Dialog Message</label>
          <input
            type="text"
            value={dialogMessage}
            onChange={(e) => setDialogMessage(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md"
          />
        </div>
        <div className="flex flex-wrap gap-2 mb-4">
          <button
            onClick={handleCustomDialog}
            className="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600"
          >
            Custom Dialog
          </button>
          <button
            onClick={handleConfirmationDialog}
            className="px-4 py-2 bg-orange-500 text-white rounded hover:bg-orange-600"
          >
            Confirmation Dialog
          </button>
          <button
            onClick={handleErrorDialog}
            className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
          >
            Error Dialog
          </button>
          <button
            onClick={handleWarningDialog}
            className="px-4 py-2 bg-yellow-500 text-white rounded hover:bg-yellow-600"
          >
            Warning Dialog
          </button>
          <button
            onClick={handleInfoDialog}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Info Dialog
          </button>
        </div>
      </div>

      {/* Convenience Dialogs Section */}
      <div className="mb-8">
        <h3 className="text-xl font-semibold mb-4">Convenience Dialogs</h3>
        <div className="flex flex-wrap gap-2 mb-4">
          <button
            onClick={handleDeleteConfirmation}
            className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
          >
            Delete Confirmation
          </button>
          <button
            onClick={handleSaveConfirmation}
            className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
          >
            Save Confirmation
          </button>
          <button
            onClick={handleExitConfirmation}
            className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
          >
            Exit Confirmation
          </button>
        </div>
      </div>

      {/* System Tray Section */}
      <div className="mb-8">
        <h3 className="text-xl font-semibold mb-4">System Tray</h3>
        <div className="flex flex-wrap gap-2 mb-4">
          <button
            onClick={handleCreateTray}
            disabled={trayCreated}
            className={`px-4 py-2 text-white rounded ${
              trayCreated 
                ? 'bg-gray-400 cursor-not-allowed' 
                : 'bg-green-500 hover:bg-green-600'
            }`}
          >
            Create System Tray
          </button>
          <button
            onClick={handleDestroyTray}
            disabled={!trayCreated}
            className={`px-4 py-2 text-white rounded ${
              !trayCreated 
                ? 'bg-gray-400 cursor-not-allowed' 
                : 'bg-red-500 hover:bg-red-600'
            }`}
          >
            Destroy System Tray
          </button>
          <button
            onClick={handleUpdateTrayTooltip}
            disabled={!trayCreated}
            className={`px-4 py-2 text-white rounded ${
              !trayCreated 
                ? 'bg-gray-400 cursor-not-allowed' 
                : 'bg-blue-500 hover:bg-blue-600'
            }`}
          >
            Update Tooltip
          </button>
          <button
            onClick={handleTrayBalloon}
            disabled={!trayCreated}
            className={`px-4 py-2 text-white rounded ${
              !trayCreated 
                ? 'bg-gray-400 cursor-not-allowed' 
                : 'bg-purple-500 hover:bg-purple-600'
            }`}
          >
            Show Balloon (Windows)
          </button>
        </div>
        {trayCreated && (
          <p className="text-green-600 text-sm">
            ✅ System tray is active. Check your system tray area for the Epos icon.
          </p>
        )}
      </div>

      <div className="bg-gray-100 p-4 rounded-lg">
        <h4 className="font-semibold mb-2">Notes:</h4>
        <ul className="text-sm text-gray-700 space-y-1">
          <li>• All notifications and dialogs use the custom Epos.png icon</li>
          <li>• System notifications appear in the OS notification area</li>
          <li>• Dialogs are modal and block interaction until dismissed</li>
          <li>• System tray provides quick access to the application</li>
          <li>• Tray balloon notifications are Windows-specific</li>
          <li>• Check the browser console for detailed results</li>
        </ul>
      </div>
    </div>
  );
};
