import React, { useState, useCallback } from "react";
import { Button } from "../button";
import type { Merchant, MerchantBank, Bank } from "../../types/merchant";

interface MerchantBankTabProps {
  merchantBanks: MerchantBank[];
  banks: Bank[];
  onAddBank: (bankData: Omit<MerchantBank, "merchant_bank_id" | "create_dt" | "update_dt">) => Promise<void>;
  onUpdateBank: (bankId: number, bankData: Omit<MerchantBank, "merchant_bank_id" | "create_dt" | "create_by">) => Promise<void>;
  onDeleteBank: (bankId: number) => Promise<void>;
  editingMerchant: Merchant | null;
  readOnly?: boolean;
}

export function MerchantBankTab({
  merchantBanks,
  banks,
  onAddBank,
  onUpdateBank,
  onDeleteBank,
  editingMerchant,
  readOnly = false
}: MerchantBankTabProps) {
  const [isAddingBank, setIsAddingBank] = useState(false);
  const [editingBankId, setEditingBankId] = useState<number | null>(null);
  const [bankFormData, setBankFormData] = useState({
    bank_id: 0,
    bank_account_no: "",
    bank_account_name: "",
    bank_branch_name: "",
    active: true,
  });

  // Memoized input change handler to prevent focus loss
  const handleBankInputChange = useCallback((field: string, value: any) => {
    setBankFormData(prev => ({ ...prev, [field]: value }));
  }, []);

  const handleAddBank = useCallback(() => {
    setBankFormData({
      bank_id: banks.length > 0 ? banks[0].bank_id : 0,
      bank_account_no: "",
      bank_account_name: "",
      bank_branch_name: "",
      active: true,
    });
    setIsAddingBank(true);
    setEditingBankId(null);
  }, [banks]);

  const handleEditBank = useCallback((bank: MerchantBank) => {
    setBankFormData({
      bank_id: bank.bank_id,
      bank_account_no: bank.bank_account_no,
      bank_account_name: bank.bank_account_name || "",
      bank_branch_name: bank.bank_branch_name || "",
      active: bank.active,
    });
    setEditingBankId(bank.merchant_bank_id || null);
    setIsAddingBank(true);
  }, []);

  const handleCancelEdit = useCallback(() => {
    setIsAddingBank(false);
    setEditingBankId(null);
    setBankFormData({
      bank_id: 0,
      bank_account_no: "",
      bank_account_name: "",
      bank_branch_name: "",
      active: true,
    });
  }, []);

  const handleSaveBank = useCallback(async () => {
    if (!bankFormData.bank_id || !bankFormData.bank_account_no.trim()) {
      alert("Please fill in required fields");
      return;
    }

    try {
      if (editingBankId) {
        // Update existing bank
        await onUpdateBank(editingBankId, {
          merchant_id: editingMerchant?.merchant_id || 0,
          bank_id: bankFormData.bank_id,
          bank_account_no: bankFormData.bank_account_no,
          bank_account_name: bankFormData.bank_account_name || undefined,
          bank_branch_name: bankFormData.bank_branch_name || undefined,
          active: bankFormData.active,
        });
      } else {
        // Add new bank
        await onAddBank({
          merchant_id: editingMerchant?.merchant_id || 0,
          bank_id: bankFormData.bank_id,
          bank_account_no: bankFormData.bank_account_no,
          bank_account_name: bankFormData.bank_account_name || undefined,
          bank_branch_name: bankFormData.bank_branch_name || undefined,
          active: bankFormData.active,
          create_by: "SYSTEM",
        });
      }
      
      handleCancelEdit();
    } catch (error) {
      console.error("Error saving bank:", error);
    }
  }, [bankFormData, editingBankId, editingMerchant?.merchant_id, onUpdateBank, onAddBank, handleCancelEdit]);

  const getBankName = useCallback((bankId?: number) => {
    if (!bankId) return "-";
    const bank = banks.find(b => b.bank_id === bankId);
    return bank ? `${bank.bank_code} - ${bank.bank_name_en || bank.bank_name_th}` : "Unknown Bank";
  }, [banks]);

  if (!editingMerchant?.merchant_id) {
    return (
      <div className="text-center py-8">
        <div className="text-gray-500 mb-4">
          <svg className="w-12 h-12 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <p className="text-lg font-medium">Save Merchant Details First</p>
          <p className="text-sm">Please save the merchant details before adding bank accounts.</p>
        </div>
      </div>
    );
  }



  // Format date values
  const formatDate = (date?: string | Date) => {
    if (!date) return "-";
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // If in read-only mode, show a comprehensive view
  if (readOnly) {
    return (
      <div className="space-y-6">
        <div>
          <h4 className="text-md font-semibold text-gray-900 mb-4 pb-2 border-b border-gray-100">
            Bank Accounts ({merchantBanks.length})
          </h4>

          {merchantBanks.length === 0 ? (
            <div className="text-center py-8">
              <div className="text-gray-500 mb-4">
                <svg className="w-12 h-12 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                </svg>
                <p className="text-lg font-medium">No Bank Accounts</p>
                <p className="text-sm">This merchant has no bank accounts configured.</p>
              </div>
            </div>
          ) : (
            <div className="grid grid-cols-1 gap-6">
              {merchantBanks.map((bank, index) => (
                <div key={bank.merchant_bank_id || index} className="bg-white border border-gray-200 rounded-lg p-6">
                  <div className="flex justify-between items-start mb-4">
                    <h5 className="text-md font-medium text-gray-900">Bank Account #{index + 1}</h5>
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      bank.active ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"
                    }`}>
                      {bank.active ? "Active" : "Inactive"}
                    </span>
                  </div>

                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Bank</label>
                      <p className="text-sm text-gray-900 bg-gray-50 p-3 rounded-lg">{getBankName(bank.bank_id)}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Account Number</label>
                      <p className="text-sm text-gray-900 bg-gray-50 p-3 rounded-lg font-mono">{bank.bank_account_no}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Account Name</label>
                      <p className="text-sm text-gray-900 bg-gray-50 p-3 rounded-lg">{bank.bank_account_name || "-"}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Branch Name</label>
                      <p className="text-sm text-gray-900 bg-gray-50 p-3 rounded-lg">{bank.bank_branch_name || "-"}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    );
  }

  // Original form view for edit/create mode
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium text-gray-900">Bank Accounts</h3>
        {!readOnly && (
          <Button
            onClick={handleAddBank}
            variant="primary"
            size="sm"
            className="inline-flex items-center gap-2"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
            </svg>
            Add Bank Account
          </Button>
        )}
      </div>

      {/* Add/Edit Bank Form */}
      {isAddingBank && (
        <div className="bg-gray-50 p-4 rounded-lg border">
          <h4 className="text-md font-medium text-gray-900 mb-4">
            {editingBankId ? "Edit Bank Account" : "Add New Bank Account"}
          </h4>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-semibold text-gray-700 mb-2">
                Bank <span className="text-red-500">*</span>
              </label>
              <select
                value={bankFormData.bank_id}
                onChange={(e) => handleBankInputChange("bank_id", parseInt(e.target.value))}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                required
              >
                <option value={0}>Select a bank</option>
                {banks.filter(bank => bank.active).map((bank) => (
                  <option key={bank.bank_id} value={bank.bank_id}>
                    {bank.bank_code} - {bank.bank_name_en || bank.bank_name_th}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-semibold text-gray-700 mb-2">
                Account Number <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                value={bankFormData.bank_account_no}
                onChange={(e) => handleBankInputChange("bank_account_no", e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Enter account number"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-semibold text-gray-700 mb-2">
                Account Name
              </label>
              <input
                type="text"
                value={bankFormData.bank_account_name}
                onChange={(e) => handleBankInputChange("bank_account_name", e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Enter account name"
              />
            </div>

            <div>
              <label className="block text-sm font-semibold text-gray-700 mb-2">
                Branch Name
              </label>
              <input
                type="text"
                value={bankFormData.bank_branch_name}
                onChange={(e) => handleBankInputChange("bank_branch_name", e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Enter branch name"
              />
            </div>

            <div className="md:col-span-2">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="bank-active"
                  checked={bankFormData.active}
                  onChange={(e) => handleBankInputChange("active", e.target.checked)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="bank-active" className="ml-2 block text-sm text-gray-900">
                  Active
                </label>
              </div>
            </div>
          </div>

          <div className="flex justify-end gap-3 mt-4">
            <Button
              onClick={handleCancelEdit}
              variant="secondary"
              size="sm"
            >
              Cancel
            </Button>
            <Button
              onClick={handleSaveBank}
              variant="primary"
              size="sm"
            >
              {editingBankId ? "Update" : "Add"} Bank Account
            </Button>
          </div>
        </div>
      )}

      {/* Bank Accounts List */}
      {merchantBanks.length === 0 ? (
        <div className="text-center py-8">
          <div className="text-gray-500 mb-4">
            <svg className="w-12 h-12 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
            </svg>
            <p className="text-lg font-medium">No Bank Accounts</p>
            <p className="text-sm">Add bank accounts for this merchant.</p>
          </div>
        </div>
      ) : (
        <div className="space-y-4">
          {merchantBanks.map((bank) => (
            <div key={bank.merchant_bank_id} className="bg-white border border-gray-200 rounded-lg p-4">
              <div className="flex justify-between items-start">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <h4 className="text-md font-medium text-gray-900">
                      {getBankName(bank.bank_id)}
                    </h4>
                    <span
                      className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        bank.active
                          ? "bg-green-100 text-green-800"
                          : "bg-red-100 text-red-800"
                      }`}
                    >
                      {bank.active ? "Active" : "Inactive"}
                    </span>
                  </div>
                  <div className="text-sm text-gray-600 space-y-1">
                    <p><span className="font-medium">Account Number:</span> {bank.bank_account_no}</p>
                    {bank.bank_account_name && (
                      <p><span className="font-medium">Account Name:</span> {bank.bank_account_name}</p>
                    )}
                    {bank.bank_branch_name && (
                      <p><span className="font-medium">Branch:</span> {bank.bank_branch_name}</p>
                    )}
                  </div>
                </div>
                {!readOnly && (
                  <div className="flex items-center gap-2 ml-4">
                    <Button
                      onClick={() => handleEditBank(bank)}
                      variant="secondary"
                      size="sm"
                      className="inline-flex items-center gap-1"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                      </svg>
                      Edit
                    </Button>
                    <Button
                      onClick={() => bank.merchant_bank_id && onDeleteBank(bank.merchant_bank_id)}
                      variant="danger"
                      size="sm"
                      className="inline-flex items-center gap-1"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                      </svg>
                      Delete
                    </Button>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
