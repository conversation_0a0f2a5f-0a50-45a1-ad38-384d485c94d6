import type { Merchant, Group, Zone, Product, Category } from "../../types/merchant";

interface ValidationError {
  field: string;
  message: string;
}

interface MerchantDetailsTabProps {
  formData: Omit<Merchant, "merchant_id" | "create_dt" | "update_dt">;
  onInputChange: (field: keyof Merchant, value: any) => void;
  editingMerchant: Merchant | null;
  groups: Group[];
  zones: Zone[];
  products: Product[];
  categories: Category[];
  mainMerchants: {merchant_id: number, merchant_name: string}[];
  readOnly?: boolean;
  validationErrors?: ValidationError[];
}

export function MerchantDetailsTab({
  formData,
  onInputChange,
  editingMerchant,
  groups,
  zones,
  products,
  categories,
  mainMerchants,
  readOnly = false,
  validationErrors = [],
}: MerchantDetailsTabProps) {

  // Helper function to get validation error for a field
  const getFieldError = (fieldName: string): string | undefined => {
    const error = validationErrors.find(err => err.field === fieldName);
    return error?.message;
  };

  // Helper function to get input class with error styling
  const getInputClassName = (fieldName: string, baseClassName: string): string => {
    const hasError = getFieldError(fieldName);
    const errorClass = hasError ? "border-red-500 focus:ring-red-500 focus:border-red-500" : "border-gray-300 focus:ring-blue-500 focus:border-blue-500";
    const readOnlyClass = readOnly ? "bg-gray-50 cursor-not-allowed" : "";
    return `${baseClassName} ${errorClass} ${readOnlyClass}`.trim();
  };
  // Helper functions for read-only display
  const getGroupName = (groupId?: number) => {
    if (!groupId) return "-";
    const group = groups.find(g => g.group_id === groupId);
    return group ? group.group_name : "-";
  };

  const getZoneName = (zoneId?: number) => {
    if (!zoneId) return "-";
    const zone = zones.find(z => z.zone_id === zoneId);
    return zone ? zone.zone_name : "-";
  };

  const getProductName = (productId?: number) => {
    if (!productId) return "-";
    const product = products.find(p => p.product_id === productId);
    return product ? product.product_name : "-";
  };

  const getCategoryName = (categoryId?: number) => {
    if (!categoryId) return "-";
    const category = categories.find(c => c.category_id === categoryId);
    return category ? category.category_name : "-";
  };

  const getParentMerchantName = (parentId?: number) => {
    if (!parentId) return "-";
    const parent = mainMerchants.find(m => m.merchant_id === parentId);
    return parent ? parent.merchant_name : "-";
  };

  // Format values for display
  const formatCurrency = (value?: number) => {
    if (value === undefined || value === null) return "-";
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2
    }).format(value);
  };

  const formatPercentage = (value?: number) => {
    if (value === undefined || value === null) return "-";
    return `${value}%`;
  };

  const formatDate = (date?: string | Date) => {
    if (!date) return "-";
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // If in read-only mode, show a comprehensive view
  if (readOnly) {
    return (
      <div className="space-y-8">
        {/* Basic Information */}
        <div>
          <h4 className="text-md font-semibold text-gray-900 mb-4 pb-2 border-b border-gray-100">
            Basic Information
          </h4>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Merchant Name</label>
              <p className="text-sm text-gray-900 bg-gray-50 p-3 rounded-lg font-medium">{formData.merchant_name}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Merchant Type</label>
              <div className="flex items-center">
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                  formData.merchant_type === "main"
                    ? "bg-blue-100 text-blue-800"
                    : "bg-purple-100 text-purple-800"
                }`}>
                  {formData.merchant_type === "main" ? "Main Merchant" : "Sub Merchant"}
                </span>
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">TID</label>
              <p className="text-sm text-gray-900 bg-gray-50 p-3 rounded-lg">{formData.merchant_vat || "-"}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Merchant ID</label>
              <p className="text-sm text-gray-900 bg-gray-50 p-3 rounded-lg">{formData.merchant_ref || "-"}</p>
            </div>
            {formData.merchant_type === "sub" && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Parent Merchant</label>
                <p className="text-sm text-gray-900 bg-gray-50 p-3 rounded-lg">{getParentMerchantName(formData.parent_merchant_id)}</p>
              </div>
            )}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Sub Name</label>
              <p className="text-sm text-gray-900 bg-gray-50 p-3 rounded-lg">{formData.merchant_sub_name || "-"}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">MCC Code</label>
              <p className="text-sm text-gray-900 bg-gray-50 p-3 rounded-lg">{formData.merchant_mcc || "-"}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">WeChat ID</label>
              <p className="text-sm text-gray-900 bg-gray-50 p-3 rounded-lg">{formData.merchant_id_wechat || "-"}</p>
            </div>
            {/* <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
              <div className="flex items-center">
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                  formData.active ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"
                }`}>
                  {formData.active ? "Active" : "Inactive"}
                </span>
              </div>
            </div> */}
          </div>
        </div>

        {/* Contact Information */}
        <div>
          <h4 className="text-md font-semibold text-gray-900 mb-4 pb-2 border-b border-gray-100">
            Contact Information
          </h4>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Phone</label>
              <p className="text-sm text-gray-900 bg-gray-50 p-3 rounded-lg">{formData.phone || "-"}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
              <p className="text-sm text-gray-900 bg-gray-50 p-3 rounded-lg">{formData.email || "-"}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Zip Code</label>
              <p className="text-sm text-gray-900 bg-gray-50 p-3 rounded-lg">{formData.zipcode || "-"}</p>
            </div>
            <div className="md:col-span-2 lg:col-span-3">
              <label className="block text-sm font-medium text-gray-700 mb-1">Address</label>
              <p className="text-sm text-gray-900 bg-gray-50 p-3 rounded-lg">{formData.address || "-"}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Contact Person</label>
              <p className="text-sm text-gray-900 bg-gray-50 p-3 rounded-lg">{formData.contact_person || "-"}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Contact Email</label>
              <p className="text-sm text-gray-900 bg-gray-50 p-3 rounded-lg">{formData.contact_email || "-"}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Contact Phone</label>
              <p className="text-sm text-gray-900 bg-gray-50 p-3 rounded-lg">{formData.contact_phone || "-"}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Contact Fax</label>
              <p className="text-sm text-gray-900 bg-gray-50 p-3 rounded-lg">{formData.contact_fax || "-"}</p>
            </div>
          </div>
        </div>

        {/* Business Configuration */}
        <div>
          <h4 className="text-md font-semibold text-gray-900 mb-4 pb-2 border-b border-gray-100">
            Business Configuration
          </h4>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Group</label>
              <p className="text-sm text-gray-900 bg-gray-50 p-3 rounded-lg">{getGroupName(formData.group_id)}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Zone</label>
              <p className="text-sm text-gray-900 bg-gray-50 p-3 rounded-lg">{getZoneName(formData.zone_id)}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Product</label>
              <p className="text-sm text-gray-900 bg-gray-50 p-3 rounded-lg">{getProductName(formData.product_id)}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Category</label>
              <p className="text-sm text-gray-900 bg-gray-50 p-3 rounded-lg">{getCategoryName(formData.category_id)}</p>
            </div>
          </div>
        </div>

        {/* Financial Settings */}
        <div>
          <h4 className="text-md font-semibold text-gray-900 mb-4 pb-2 border-b border-gray-100">
            Financial Settings
          </h4>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Minimum Transfer Rate</label>
              <p className="text-sm text-gray-900 bg-gray-50 p-3 rounded-lg">{formatPercentage(formData.rate_min_transfer)}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Transfer Fee</label>
              <p className="text-sm text-gray-900 bg-gray-50 p-3 rounded-lg">{formatCurrency(formData.transfer_fee)}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Settlement Fee</label>
              <p className="text-sm text-gray-900 bg-gray-50 p-3 rounded-lg">{formatCurrency(formData.settlement_fee)}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Withholding Tax</label>
              <p className="text-sm text-gray-900 bg-gray-50 p-3 rounded-lg">{formatPercentage(formData.withholding_tax)}</p>
            </div>
          </div>
        </div>

        {/* Invoice Information */}
        <div>
          <h4 className="text-md font-semibold text-gray-900 mb-4 pb-2 border-b border-gray-100">
            Invoice Information
          </h4>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Invoice Name</label>
              <p className="text-sm text-gray-900 bg-gray-50 p-3 rounded-lg">{formData.invoice_name || "-"}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Invoice Tax</label>
              <p className="text-sm text-gray-900 bg-gray-50 p-3 rounded-lg">{formData.invoice_tax || "-"}</p>
            </div>
            <div className="md:col-span-2 lg:col-span-1">
              <label className="block text-sm font-medium text-gray-700 mb-1">Invoice Address</label>
              <p className="text-sm text-gray-900 bg-gray-50 p-3 rounded-lg">{formData.invoice_address || "-"}</p>
            </div>
          </div>
        </div>

        {/* System Information */}
        {editingMerchant && (
          <div>
            <h4 className="text-md font-semibold text-gray-900 mb-4 pb-2 border-b border-gray-100">
              System Information
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Created Date</label>
                <p className="text-sm text-gray-900 bg-gray-50 p-3 rounded-lg">{formatDate(editingMerchant.create_dt)}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Created By</label>
                <p className="text-sm text-gray-900 bg-gray-50 p-3 rounded-lg">{editingMerchant.create_by || "-"}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Last Updated</label>
                <p className="text-sm text-gray-900 bg-gray-50 p-3 rounded-lg">{formatDate(editingMerchant.update_dt)}</p>
              </div>
              <div className="md:col-span-2 lg:col-span-3">
                <label className="block text-sm font-medium text-gray-700 mb-1">Remarks</label>
                <p className="text-sm text-gray-900 bg-gray-50 p-3 rounded-lg">{formData.remark || "-"}</p>
              </div>
            </div>
          </div>
        )}
      </div>
    );
  }

  // Original form view for edit/create mode
  return (
    <div className="space-y-6">
      {/* Basic Information */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4">
          Basic Information
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Merchant Name <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              value={formData.merchant_name}
              onChange={(e) => onInputChange("merchant_name", e.target.value)}
              className={getInputClassName("merchant_name", "w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:border-transparent")}
              placeholder="Enter merchant name"
              required
              readOnly={readOnly}
              disabled={readOnly}
            />
            {getFieldError("merchant_name") && (
              <p className="mt-1 text-sm text-red-600">{getFieldError("merchant_name")}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Merchant Type <span className="text-red-500">*</span>
            </label>
            <select
              value={formData.merchant_type}
              onChange={(e) => {
                const newType = e.target.value as "main" | "sub";
                onInputChange("merchant_type", newType);
                // Clear parent merchant when switching to main type
                if (newType === "main") {
                  onInputChange("parent_merchant_id", undefined);
                }
              }}
              className={getInputClassName("merchant_type", "w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:border-transparent")}
              required
              disabled={readOnly}
            >
              <option value="main">Main Merchant</option>
              <option value="sub">Sub Merchant</option>
            </select>
            {getFieldError("merchant_type") && (
              <p className="mt-1 text-sm text-red-600">{getFieldError("merchant_type")}</p>
            )}
          </div>

          {/* Parent Merchant Dropdown - Only show for sub merchants */}
          {formData.merchant_type === "sub" && (
            <div>
              <label className="block text-sm font-semibold text-gray-700 mb-2">
                Parent Merchant <span className="text-red-500">*</span>
              </label>
              <select
                value={formData.parent_merchant_id || ""}
                onChange={(e) =>
                  onInputChange("parent_merchant_id", e.target.value ? parseInt(e.target.value) : undefined)
                }
                className={getInputClassName("parent_merchant_id", "w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:border-transparent")}
                required
                disabled={readOnly}
              >
                <option value="">Select Parent Merchant</option>
                {mainMerchants.map((merchant) => (
                  <option key={merchant.merchant_id} value={merchant.merchant_id}>
                    {merchant.merchant_name}
                  </option>
                ))}
              </select>
              {getFieldError("parent_merchant_id") && (
                <p className="mt-1 text-sm text-red-600">{getFieldError("parent_merchant_id")}</p>
              )}
              {mainMerchants.length === 0 && (
                <p className="mt-1 text-sm text-yellow-600">
                  No main merchants available. Please create a main merchant first.
                </p>
              )}
            </div>
          )}

          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Tax ID
            </label>
            <input
              type="text"
              value={formData.merchant_vat || ""}
              onChange={(e) => onInputChange("merchant_vat", e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter Tax ID number"
              readOnly={readOnly}
              disabled={readOnly}
            />
          </div>

          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Merchant ID
            </label>
            <input
              type="text"
              value={formData.merchant_ref || ""}
              onChange={(e) => onInputChange("merchant_ref", e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter merchant id"
              readOnly={readOnly}
              disabled={readOnly}
            />
          </div>

          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Sub Merchant Name
            </label>
            <input
              type="text"
              value={formData.merchant_sub_name || ""}
              onChange={(e) =>
                onInputChange("merchant_sub_name", e.target.value)
              }
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter sub merchant name"
              readOnly={readOnly}
              disabled={readOnly}
            />
          </div>

          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              MCC Code
            </label>
            <input
              type="text"
              value={formData.merchant_mcc || ""}
              onChange={(e) => onInputChange("merchant_mcc", e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter MCC code"
              readOnly={readOnly}
              disabled={readOnly}
            />
          </div>

          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              WeChat Merchant ID <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              value={formData.merchant_id_wechat || ""}
              onChange={(e) =>
                onInputChange("merchant_id_wechat", e.target.value)
              }
              className={getInputClassName("merchant_id_wechat", "w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:border-transparent")}
              placeholder="Enter WeChat merchant ID"
              required
              readOnly={readOnly}
              disabled={readOnly}
            />
            {getFieldError("merchant_id_wechat") && (
              <p className="mt-1 text-sm text-red-600">{getFieldError("merchant_id_wechat")}</p>
            )}
          </div>
        </div>
      </div>

      {/* Contact Information */}
      <div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Phone
            </label>
            <input
              type="tel"
              value={formData.phone || ""}
              onChange={(e) => onInputChange("phone", e.target.value)}
              className={getInputClassName("phone", "w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:border-transparent")}
              placeholder="Enter phone number"
              readOnly={readOnly}
              disabled={readOnly}
            />
            {getFieldError("phone") && (
              <p className="mt-1 text-sm text-red-600">{getFieldError("phone")}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Email
            </label>
            <input
              type="email"
              value={formData.email || ""}
              onChange={(e) => onInputChange("email", e.target.value)}
              className={getInputClassName("email", "w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:border-transparent")}
              placeholder="Enter email address"
              readOnly={readOnly}
              disabled={readOnly}
            />
            {getFieldError("email") && (
              <p className="mt-1 text-sm text-red-600">{getFieldError("email")}</p>
            )}
          </div>

          <div className="md:col-span-2">
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Address
            </label>
            <textarea
              value={formData.address || ""}
              onChange={(e) => onInputChange("address", e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter address"
              rows={3}
            />
          </div>

          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Zip Code
            </label>
            <input
              type="text"
              value={formData.zipcode || ""}
              onChange={(e) => onInputChange("zipcode", e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter zip code"
            />
          </div>
        </div>
      </div>
      {/* Contact Information */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4">
          Contact Information
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Contact Person
            </label>
            <input
              type="text"
              value={formData.contact_person || ""}
              onChange={(e) => onInputChange("contact_person", e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter contact person name"
            />
          </div>

          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Contact Email
            </label>
            <input
              type="email"
              value={formData.contact_email || ""}
              onChange={(e) => onInputChange("contact_email", e.target.value)}
              className={getInputClassName("contact_email", "w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:border-transparent")}
              placeholder="Enter contact email"
              readOnly={readOnly}
              disabled={readOnly}
            />
            {getFieldError("contact_email") && (
              <p className="mt-1 text-sm text-red-600">{getFieldError("contact_email")}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Contact Phone
            </label>
            <input
              type="tel"
              value={formData.contact_phone || ""}
              onChange={(e) => onInputChange("contact_phone", e.target.value)}
              className={getInputClassName("contact_phone", "w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:border-transparent")}
              placeholder="Enter contact phone"
              readOnly={readOnly}
              disabled={readOnly}
            />
            {getFieldError("contact_phone") && (
              <p className="mt-1 text-sm text-red-600">{getFieldError("contact_phone")}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Contact Fax
            </label>
            <input
              type="tel"
              value={formData.contact_fax || ""}
              onChange={(e) => onInputChange("contact_fax", e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter contact fax"
            />
          </div>
        </div>
      </div>

      {/* Invoice Information */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4">
          Invoice Information
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Invoice Name
            </label>
            <input
              type="text"
              value={formData.invoice_name || ""}
              onChange={(e) => onInputChange("invoice_name", e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter invoice name"
            />
          </div>

          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Invoice Tax ID
            </label>
            <input
              type="text"
              value={formData.invoice_tax || ""}
              onChange={(e) => onInputChange("invoice_tax", e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter invoice tax ID"
            />
          </div>

          <div className="md:col-span-2">
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Invoice Address
            </label>
            <textarea
              value={formData.invoice_address || ""}
              onChange={(e) => onInputChange("invoice_address", e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter invoice address"
              rows={3}
            />
          </div>
        </div>
      </div>

      {/* Classification Settings */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4">
          Classification Settings
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Group
            </label>
            <select
              value={formData.group_id?.toString() || ""}
              onChange={(e) => {
                const value = e.target.value;
                onInputChange(
                  "group_id",
                  value === "" ? undefined : parseInt(value)
                );
              }}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">Select a group</option>
              {groups.map((group) => (
                <option key={group.group_id} value={group.group_id}>
                  {group.group_name}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Zone
            </label>
            <select
              value={formData.zone_id?.toString() || ""}
              onChange={(e) => {
                const value = e.target.value;
                onInputChange(
                  "zone_id",
                  value === "" ? undefined : parseInt(value)
                );
              }}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">Select a zone</option>
              {zones.map((zone) => (
                <option key={zone.zone_id} value={zone.zone_id}>
                  {zone.zone_name} ({zone.zone_code})
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Product
            </label>
            <select
              value={formData.product_id?.toString() || ""}
              onChange={(e) => {
                const value = e.target.value;
                onInputChange(
                  "product_id",
                  value === "" ? undefined : parseInt(value)
                );
              }}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">Select a product</option>
              {products.map((product) => (
                <option key={product.product_id} value={product.product_id}>
                  {product.product_name}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Category
            </label>
            <select
              value={formData.category_id?.toString() || ""}
              onChange={(e) => {
                const value = e.target.value;
                onInputChange(
                  "category_id",
                  value === "" ? undefined : parseInt(value)
                );
              }}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">Select a category</option>
              {categories.map((category) => (
                <option key={category.category_id} value={category.category_id}>
                  {category.category_name}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Financial Settings */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4">
          Financial Settings
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Min Transfer Rate
            </label>
            <input
              type="number"
              step="0.01"
              value={formData.rate_min_transfer?.toString() || ""}
              onChange={(e) => {
                const value = e.target.value;
                const numValue = value === "" ? undefined : parseFloat(value);
                onInputChange(
                  "rate_min_transfer",
                  numValue === undefined || isNaN(numValue) ? undefined : numValue
                );
              }}
              className={getInputClassName("rate_min_transfer", "w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:border-transparent")}
              placeholder="0.00"
              readOnly={readOnly}
              disabled={readOnly}
            />
            {getFieldError("rate_min_transfer") && (
              <p className="mt-1 text-sm text-red-600">{getFieldError("rate_min_transfer")}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Transfer Fee (THB)
            </label>
            <input
              type="number"
              step="0.01"
              value={formData.transfer_fee?.toString() || ""}
              onChange={(e) => {
                const value = e.target.value;
                const numValue = value === "" ? undefined : parseFloat(value);
                onInputChange(
                  "transfer_fee",
                  numValue === undefined || isNaN(numValue) ? undefined : numValue
                );
              }}
              className={getInputClassName("transfer_fee", "w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:border-transparent")}
              placeholder="50.00"
              readOnly={readOnly}
              disabled={readOnly}
            />
            {getFieldError("transfer_fee") && (
              <p className="mt-1 text-sm text-red-600">{getFieldError("transfer_fee")}</p>
            )}
            <p className="text-xs text-gray-500 mt-1">
              Direct fee amount used in Final Net Amount calculation
            </p>
          </div>

          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Settlement Fee (THB)
            </label>
            <input
              type="number"
              step="0.01"
              value={formData.settlement_fee?.toString() || ""}
              onChange={(e) => {
                const value = e.target.value;
                const numValue = value === "" ? undefined : parseFloat(value);
                onInputChange(
                  "settlement_fee",
                  numValue === undefined || isNaN(numValue) ? undefined : numValue
                );
              }}
              className={getInputClassName("settlement_fee", "w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:border-transparent")}
              placeholder="25.00"
              readOnly={readOnly}
              disabled={readOnly}
            />
            {getFieldError("settlement_fee") && (
              <p className="mt-1 text-sm text-red-600">{getFieldError("settlement_fee")}</p>
            )}
            <p className="text-xs text-gray-500 mt-1">
              Fee for settlement processing
            </p>
          </div>

          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Withholding Tax (%)
            </label>
            <input
              type="number"
              step="0.01"
              value={formData.withholding_tax?.toString() || ""}
              onChange={(e) => {
                const value = e.target.value;
                const numValue = value === "" ? undefined : parseFloat(value);
                onInputChange(
                  "withholding_tax",
                  numValue === undefined || isNaN(numValue) ? undefined : numValue
                );
              }}
              className={getInputClassName("withholding_tax", "w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:border-transparent")}
              placeholder="3.00"
              readOnly={readOnly}
              disabled={readOnly}
            />
            {getFieldError("withholding_tax") && (
              <p className="mt-1 text-sm text-red-600">{getFieldError("withholding_tax")}</p>
            )}
            <p className="text-xs text-gray-500 mt-1">
              Percentage rate used to calculate withholding tax on MDR amount
            </p>
          </div>
        </div>
      </div>

      {/* Additional Information */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4">
          Additional Information
        </h3>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Remarks
            </label>
            <textarea
              value={formData.remark || ""}
              onChange={(e) => onInputChange("remark", e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter any additional remarks"
              rows={3}
            />
          </div>

          <div className="flex items-center">
            <input
              type="checkbox"
              id="active"
              checked={formData.active}
              onChange={(e) => onInputChange("active", e.target.checked)}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label
              htmlFor="active"
              className="ml-2 block text-sm text-gray-900"
            >
              Active
            </label>
          </div>
        </div>
      </div>
    </div>
  );
}