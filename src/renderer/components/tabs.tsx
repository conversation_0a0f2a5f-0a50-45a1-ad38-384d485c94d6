import React, { useState, ReactNode } from 'react';

interface TabItem {
  id: string;
  label: string;
  content: ReactNode;
  icon?: ReactNode;
}

interface TabsProps {
  tabs: TabItem[];
  defaultTab?: string;
  className?: string;
  tabClassName?: string;
  contentClassName?: string;
  onChange?: (tabId: string) => void;
}

export function Tabs({ 
  tabs, 
  defaultTab, 
  className = '', 
  tabClassName = '',
  contentClassName = '',
  onChange 
}: TabsProps) {
  const [activeTab, setActiveTab] = useState(defaultTab || tabs[0]?.id || '');

  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
    onChange?.(tabId);
  };

  const activeTabContent = tabs.find(tab => tab.id === activeTab)?.content;

  return (
    <div className={`w-full ${className}`}>
      {/* Tab Headers */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8" aria-label="Tabs">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => handleTabChange(tab.id)}
              className={`
                group inline-flex items-center py-2 px-1 border-b-2 font-medium text-sm transition-colors
                ${activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }
                ${tabClassName}
              `}
              aria-current={activeTab === tab.id ? 'page' : undefined}
            >
              {tab.icon && (
                <span className={`mr-2 ${activeTab === tab.id ? 'text-blue-500' : 'text-gray-400 group-hover:text-gray-500'}`}>
                  {tab.icon}
                </span>
              )}
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className={`mt-6 ${contentClassName}`}>
        {activeTabContent}
      </div>
    </div>
  );
}

// Alternative vertical tabs component
interface VerticalTabsProps extends TabsProps {
  tabPosition?: 'left' | 'right';
}

export function VerticalTabs({ 
  tabs, 
  defaultTab, 
  className = '', 
  tabClassName = '',
  contentClassName = '',
  tabPosition = 'left',
  onChange 
}: VerticalTabsProps) {
  const [activeTab, setActiveTab] = useState(defaultTab || tabs[0]?.id || '');

  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
    onChange?.(tabId);
  };

  const activeTabContent = tabs.find(tab => tab.id === activeTab)?.content;

  const tabsElement = (
    <div className="space-y-1">
      {tabs.map((tab) => (
        <button
          key={tab.id}
          onClick={() => handleTabChange(tab.id)}
          className={`
            group w-full flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors
            ${activeTab === tab.id
              ? 'bg-blue-100 text-blue-700'
              : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
            }
            ${tabClassName}
          `}
        >
          {tab.icon && (
            <span className={`mr-3 ${activeTab === tab.id ? 'text-blue-500' : 'text-gray-400 group-hover:text-gray-500'}`}>
              {tab.icon}
            </span>
          )}
          {tab.label}
        </button>
      ))}
    </div>
  );

  const contentElement = (
    <div className={`flex-1 ${contentClassName}`}>
      {activeTabContent}
    </div>
  );

  return (
    <div className={`flex ${className}`}>
      {tabPosition === 'left' && (
        <>
          <div className="w-48 flex-shrink-0 mr-6">
            {tabsElement}
          </div>
          {contentElement}
        </>
      )}
      {tabPosition === 'right' && (
        <>
          {contentElement}
          <div className="w-48 flex-shrink-0 ml-6">
            {tabsElement}
          </div>
        </>
      )}
    </div>
  );
}

// Simple tab component for basic use cases
interface SimpleTabsProps {
  children: React.ReactElement[];
  defaultIndex?: number;
  className?: string;
  onChange?: (index: number) => void;
}

export function SimpleTabs({ children, defaultIndex = 0, className = '', onChange }: SimpleTabsProps) {
  const [activeIndex, setActiveIndex] = useState(defaultIndex);

  const handleTabChange = (index: number) => {
    setActiveIndex(index);
    onChange?.(index);
  };

  return (
    <div className={`w-full ${className}`}>
      {/* Tab Headers */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8" aria-label="Tabs">
          {children.map((child, index) => (
            <button
              key={index}
              onClick={() => handleTabChange(index)}
              className={`
                py-2 px-1 border-b-2 font-medium text-sm transition-colors
                ${activeIndex === index
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }
              `}
              aria-current={activeIndex === index ? 'page' : undefined}
            >
              {child.props.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="mt-6">
        {children[activeIndex]}
      </div>
    </div>
  );
}

// Tab panel component for use with SimpleTabs
interface TabPanelProps {
  label: string;
  children: ReactNode;
}

export function TabPanel({ children }: TabPanelProps) {
  return <div>{children}</div>;
}
