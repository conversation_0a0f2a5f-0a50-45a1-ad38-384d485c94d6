import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { TransactionSummaryScreen } from '../transaction-summary.screen';
import { AuthProvider } from '../../contexts/AuthContext';
import { NotificationProvider } from '../../contexts/NotificationContext';

// Mock the electron utilities
jest.mock('../../utils/electron', () => ({
  safeIpcInvoke: jest.fn()
}));

// Mock the hooks
jest.mock('../../hooks/useRoleAccess', () => ({
  useMasterDataAccess: () => ({
    hasReadAccess: true,
    hasWriteAccess: true,
    userRole: 'admin'
  })
}));

// Test wrapper component
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <BrowserRouter>
    <AuthProvider>
      <NotificationProvider>
        {children}
      </NotificationProvider>
    </AuthProvider>
  </BrowserRouter>
);

describe('TransactionSummaryScreen', () => {
  beforeEach(() => {
    // Reset any mocks before each test
    jest.clearAllMocks();
  });

  test('renders transaction summary screen with header', async () => {
    render(
      <TestWrapper>
        <TransactionSummaryScreen />
      </TestWrapper>
    );

    // Check if the main header is present
    expect(screen.getByText(/STSOFT SOLUTION.*PART DELIVERY SUMMARY/)).toBeInTheDocument();
  });

  test('renders control buttons', async () => {
    render(
      <TestWrapper>
        <TransactionSummaryScreen />
      </TestWrapper>
    );

    // Check if control buttons are present
    expect(screen.getByText('🔍 Search')).toBeInTheDocument();
    expect(screen.getByText('❌ Cancel')).toBeInTheDocument();
    expect(screen.getByText('✅ Approve')).toBeInTheDocument();
    expect(screen.getByText('❌ Close')).toBeInTheDocument();
    expect(screen.getByText('🖨️ Print')).toBeInTheDocument();
    expect(screen.getByText('📊 Export')).toBeInTheDocument();
  });

  test('renders filter controls', async () => {
    render(
      <TestWrapper>
        <TransactionSummaryScreen />
      </TestWrapper>
    );

    // Check if filter controls are present
    expect(screen.getByText('Running Number:')).toBeInTheDocument();
    expect(screen.getByText('Date:')).toBeInTheDocument();
    expect(screen.getByText('Transfer')).toBeInTheDocument();
    expect(screen.getByText('Pending')).toBeInTheDocument();
  });

  test('renders tab navigation', async () => {
    render(
      <TestWrapper>
        <TransactionSummaryScreen />
      </TestWrapper>
    );

    // Check if tabs are present
    expect(screen.getByText('Transfer today')).toBeInTheDocument();
    expect(screen.getByText('Transaction')).toBeInTheDocument();
  });

  test('loads and displays mock data', async () => {
    render(
      <TestWrapper>
        <TransactionSummaryScreen />
      </TestWrapper>
    );

    // Wait for mock data to load
    await waitFor(() => {
      expect(screen.getByText('773,774.52')).toBeInTheDocument();
    });

    // Check if summary data is displayed
    expect(screen.getByText('Transfer today')).toBeInTheDocument();
    expect(screen.getByText('Pending transfer')).toBeInTheDocument();
    expect(screen.getByText('ยอดรวมทั้งสิ้น')).toBeInTheDocument();
  });

  test('switches between tabs', async () => {
    render(
      <TestWrapper>
        <TransactionSummaryScreen />
      </TestWrapper>
    );

    // Wait for data to load
    await waitFor(() => {
      expect(screen.getByText('773,774.52')).toBeInTheDocument();
    });

    // Initially should show summary table
    expect(screen.getByText('Bank')).toBeInTheDocument();
    expect(screen.getByText('Account Name')).toBeInTheDocument();

    // Click on Transaction tab
    fireEvent.click(screen.getByText('Transaction'));

    // Should now show transaction detail table
    expect(screen.getByText('Trn Date')).toBeInTheDocument();
    expect(screen.getByText('Service A/C')).toBeInTheDocument();
    expect(screen.getByText('Sum MDR')).toBeInTheDocument();
  });

  test('handles search button click', async () => {
    render(
      <TestWrapper>
        <TransactionSummaryScreen />
      </TestWrapper>
    );

    const searchButton = screen.getByText('🔍 Search');
    fireEvent.click(searchButton);

    // Should trigger loading state
    expect(screen.getByText('Loading transaction summary...')).toBeInTheDocument();
  });

  test('handles cancel button click', async () => {
    render(
      <TestWrapper>
        <TransactionSummaryScreen />
      </TestWrapper>
    );

    const cancelButton = screen.getByText('❌ Cancel');
    fireEvent.click(cancelButton);

    // Should reset form values (this would need more specific assertions based on implementation)
    expect(cancelButton).toBeInTheDocument();
  });

  test('displays table data correctly', async () => {
    render(
      <TestWrapper>
        <TransactionSummaryScreen />
      </TestWrapper>
    );

    // Wait for data to load
    await waitFor(() => {
      expect(screen.getByText('T Z W CO LTD')).toBeInTheDocument();
    });

    // Check if mock data is displayed in table
    expect(screen.getByText('T Z W CO LTD')).toBeInTheDocument();
    expect(screen.getByText('P.S. SIAM GARDEN CO.,LTD.')).toBeInTheDocument();
    expect(screen.getByText('209,426.49')).toBeInTheDocument();
  });
});
