import React, { useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { useCurrentUser } from '../hooks/useCurrentUser';
import { useUserOperations } from '../hooks/useUserOperations';
import {
  SessionStatusIndicator,
  SessionTimer,
  SessionStatusBadge
} from '../components/SessionStatusIndicator';
import {
  useSessionTimeRemaining,
  useSessionTimeoutWarning
} from '../components/SessionTimeoutWarning';
import { Button } from '../components/button';

export function SessionDemoScreen() {
  const {
    user,
    sessionId,
    expiresAt,
    getTimeUntilExpiry,
    extendSession,
    validateSession,
    logout
  } = useAuth();

  const { currentUserName, getCurrentUser } = useCurrentUser();
  const { prepareCreateData, prepareUpdateData } = useUserOperations();

  const timeRemaining = useSessionTimeRemaining();
  const { shouldShowWarning, minutesRemaining, secondsRemaining } = useSessionTimeoutWarning(15);
  const [isExtending, setIsExtending] = useState(false);
  const [isValidating, setIsValidating] = useState(false);

  const handleExtendSession = async () => {
    setIsExtending(true);
    try {
      const success = await extendSession();
      if (success) {
        alert('Session extended successfully!');
      } else {
        alert('Failed to extend session. Please login again.');
      }
    } catch (error) {
      console.error('Error extending session:', error);
      alert('Error extending session');
    } finally {
      setIsExtending(false);
    }
  };

  const handleValidateSession = async () => {
    setIsValidating(true);
    try {
      const isValid = await validateSession();
      alert(isValid ? 'Session is valid' : 'Session is invalid');
    } catch (error) {
      console.error('Error validating session:', error);
      alert('Error validating session');
    } finally {
      setIsValidating(false);
    }
  };

  const formatTime = (milliseconds: number | null): string => {
    if (!milliseconds) return 'N/A';
    
    const totalMinutes = Math.floor(milliseconds / (1000 * 60));
    const hours = Math.floor(totalMinutes / 60);
    const minutes = totalMinutes % 60;
    const seconds = Math.floor((milliseconds % (1000 * 60)) / 1000);
    
    if (hours > 0) {
      return `${hours}h ${minutes}m ${seconds}s`;
    }
    return `${minutes}m ${seconds}s`;
  };

  return (
    <div className="space-y-8">
      <div>
        <h1 className="text-2xl font-bold text-gray-900 mb-2">Session Management Demo</h1>
        <p className="text-gray-600">
          This page demonstrates the session management features with 1-hour timeout.
        </p>
      </div>

      {/* Current Session Info */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Current Session Information</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700">User</label>
            <p className="mt-1 text-sm text-gray-900">{user?.user_name}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Role</label>
            <p className="mt-1 text-sm text-gray-900">{user?.role_name || 'N/A'}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Session ID</label>
            <p className="mt-1 text-sm text-gray-900 font-mono">{sessionId}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Expires At</label>
            <p className="mt-1 text-sm text-gray-900">
              {expiresAt ? expiresAt.toLocaleString() : 'N/A'}
            </p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Time Remaining</label>
            <p className="mt-1 text-sm text-gray-900 font-mono">
              {formatTime(timeRemaining)}
            </p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Warning Status</label>
            <p className="mt-1 text-sm">
              {shouldShowWarning ? (
                <span className="text-yellow-600">
                  ⚠️ Warning: {minutesRemaining}m {secondsRemaining}s remaining
                </span>
              ) : (
                <span className="text-green-600">✅ Session OK</span>
              )}
            </p>
          </div>
        </div>
      </div>

      {/* Session Actions */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Session Actions</h2>
        <div className="flex flex-wrap gap-4">
          <Button
            onClick={handleExtendSession}
            disabled={isExtending}
            className="bg-blue-600 hover:bg-blue-700 text-white"
          >
            {isExtending ? 'Extending...' : 'Extend Session'}
          </Button>
          
          <Button
            onClick={handleValidateSession}
            disabled={isValidating}
            variant="secondary"
          >
            {isValidating ? 'Validating...' : 'Validate Session'}
          </Button>
          
          <Button
            onClick={logout}
            variant="secondary"
            className="bg-red-600 hover:bg-red-700 text-white"
          >
            Logout
          </Button>
        </div>
      </div>

      {/* Session Components Demo */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Session UI Components</h2>
        
        <div className="space-y-6">
          <div>
            <h3 className="text-md font-medium text-gray-900 mb-2">Session Status Indicator (Full)</h3>
            <SessionStatusIndicator showTimeRemaining={true} compact={false} />
          </div>
          
          <div>
            <h3 className="text-md font-medium text-gray-900 mb-2">Session Status Indicator (Compact)</h3>
            <SessionStatusIndicator showTimeRemaining={true} compact={true} />
          </div>
          
          <div>
            <h3 className="text-md font-medium text-gray-900 mb-2">Session Timer</h3>
            <SessionTimer />
          </div>
          
          <div>
            <h3 className="text-md font-medium text-gray-900 mb-2">Session Status Badge</h3>
            <SessionStatusBadge />
          </div>
        </div>
      </div>

      {/* Session Monitoring Info */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Session Monitoring Features</h2>
        <div className="prose text-sm text-gray-600">
          <ul className="list-disc list-inside space-y-2">
            <li><strong>1-Hour Session Timeout:</strong> Sessions automatically expire after 1 hour of inactivity</li>
            <li><strong>Automatic Monitoring:</strong> Session validity is checked every 5 minutes in the background</li>
            <li><strong>Timeout Warning:</strong> Users receive a warning 10 minutes before session expiry</li>
            <li><strong>Session Extension:</strong> Users can extend their session if it's still valid</li>
            <li><strong>Auto-logout:</strong> Users are automatically logged out when session expires</li>
            <li><strong>Server Validation:</strong> Session validity is verified with the server</li>
            <li><strong>Single Session:</strong> Only one active session per user is allowed</li>
            <li><strong>Force Login:</strong> New logins can terminate existing sessions</li>
          </ul>
        </div>
      </div>

      {/* User Operations Demo */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Automatic User Field Injection</h2>
        <div className="space-y-4">
          <div>
            <h3 className="text-md font-medium text-gray-900 mb-2">Current User Information</h3>
            <div className="bg-gray-50 rounded p-3">
              <p><strong>Username:</strong> {currentUserName}</p>
              <p><strong>User ID:</strong> {getCurrentUser().user_id}</p>
              <p><strong>Role:</strong> {getCurrentUser().role_name || getCurrentUser().role_code || 'N/A'}</p>
            </div>
          </div>

          <div>
            <h3 className="text-md font-medium text-gray-900 mb-2">Example: Create Data with User Fields</h3>
            <div className="bg-gray-50 rounded p-3">
              <pre className="text-sm text-gray-700">
{JSON.stringify(prepareCreateData({
  name: "Sample Record",
  active: true,
  description: "This record will have create_by automatically set"
}), null, 2)}
              </pre>
            </div>
          </div>

          <div>
            <h3 className="text-md font-medium text-gray-900 mb-2">Example: Update Data with User Fields</h3>
            <div className="bg-gray-50 rounded p-3">
              <pre className="text-sm text-gray-700">
{JSON.stringify(prepareUpdateData({
  name: "Updated Record",
  active: false,
  description: "This record will have update_by automatically set"
}), null, 2)}
              </pre>
            </div>
          </div>
        </div>
      </div>

      {/* Technical Details */}
      <div className="bg-gray-50 rounded-lg p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Technical Implementation</h2>
        <div className="prose text-sm text-gray-600">
          <ul className="list-disc list-inside space-y-2">
            <li><strong>Backend:</strong> PostgreSQL-based session tracking with timezone support</li>
            <li><strong>Frontend:</strong> React Context for session state management</li>
            <li><strong>Monitoring:</strong> Periodic validation with configurable intervals</li>
            <li><strong>Storage:</strong> Session data persisted in localStorage with expiry</li>
            <li><strong>Security:</strong> Server-side session validation and automatic cleanup</li>
            <li><strong>UX:</strong> Graceful handling of session expiry with user notifications</li>
            <li><strong>User Tracking:</strong> Automatic injection of create_by and update_by fields</li>
            <li><strong>Data Operations:</strong> Simplified hooks for CRUD operations with user context</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
