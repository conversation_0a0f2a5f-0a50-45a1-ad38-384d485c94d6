import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useNotification } from '../contexts/NotificationContext';
import { safeIpcInvoke } from '../utils/electron';
import { RoleBasedComponent } from '../components/RoleBasedComponent';

// Types for Transaction Summary using only detail table
interface TransactionSummaryDetailItem {
  id: number;
  merchant_vat: string;
  merchant_name: string;
  transaction_date: string;
  channel_type: string;
  transaction_count: number;
  total_amount: number;
  mdr_rate: number;
  mdr_amount: number;
  vat_percentage: number;
  vat_amount: number;
  net_amount: number;
  withholding_tax_rate: number;
  withhold_tax: number;
  transfer_fee: number;
  reimbursement_fee: number;
  service_fee: number;
  final_net_amount: number;
  cup_business_tax_fee: number;
  is_transfer: number;
  create_by: string;
  create_dt: string;
  update_by: string;
  update_dt: string;
}

export function TransactionSummaryFilterScreen() {
  const { user } = useAuth();
  const { showNotification } = useNotification();
  const navigate = useNavigate();

  // State variables
  const [filteredTransactions, setFilteredTransactions] = useState<TransactionSummaryDetailItem[]>([]);
  const [filteredLoading, setFilteredLoading] = useState(false);
  const [selectedFilteredIds, setSelectedFilteredIds] = useState<Set<number>>(new Set());

  // Filter states
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
  const [endDate, setEndDate] = useState(new Date().toISOString().split('T')[0]);
  const [merchantVat, setMerchantVat] = useState('');
  const [channelType, setChannelType] = useState('');
  const [transferFilter, setTransferFilter] = useState<number | undefined>(undefined);

  // Load filtered transactions based on user criteria
  const loadFilteredTransactions = async () => {
    setFilteredLoading(true);
    try {
      console.log('🔍 Loading filtered transactions with criteria:', {
        startDate: selectedDate,
        endDate: endDate,
        merchantVat,
        channelType,
        transferFilter
      });

      const result = await safeIpcInvoke('get-transaction-summary-details', {
        startDate: selectedDate,
        endDate: endDate,
        merchantVat: merchantVat || undefined,
        channelType: channelType || undefined,
        isTransfer: transferFilter,
        page: 1,
        pageSize: 1000
      });

      if (result.success) {
        setFilteredTransactions(result.data || []);
        console.log(`📋 Loaded ${result.data?.length || 0} filtered transactions`);
      } else {
        showNotification('Failed to load filtered transactions: ' + (result.error || 'Unknown error'), 'error');
        setFilteredTransactions([]);
      }
    } catch (error) {
      console.error('Error loading filtered transactions:', error);
      showNotification('Error loading filtered transactions', 'error');
      setFilteredTransactions([]);
    } finally {
      setFilteredLoading(false);
    }
  };

  const handleSearchFiltered = async () => {
    await loadFilteredTransactions();
    showNotification('Filtered transactions loaded', 'success');
  };

  const handleClearFilters = () => {
    setSelectedDate(new Date().toISOString().split('T')[0]);
    setEndDate(new Date().toISOString().split('T')[0]);
    setMerchantVat('');
    setChannelType('');
    setTransferFilter(undefined);
    setSelectedFilteredIds(new Set());
    showNotification('Filters cleared', 'info');
  };

  // Selection helper functions
  const handleSelectFiltered = (id: number, checked: boolean) => {
    const newSelected = new Set(selectedFilteredIds);
    if (checked) {
      newSelected.add(id);
    } else {
      newSelected.delete(id);
    }
    setSelectedFilteredIds(newSelected);
  };

  const handleSelectAllFiltered = (checked: boolean) => {
    if (checked) {
      const allIds = new Set(filteredTransactions.map(item => item.id));
      setSelectedFilteredIds(allIds);
    } else {
      setSelectedFilteredIds(new Set());
    }
  };

  const updateTransferStatus = async (detailId: number, isTransfer: number) => {
    if (!user?.user_name) {
      showNotification('User not authenticated', 'error');
      return;
    }

    try {
      const result = await safeIpcInvoke('update-transfer-status', detailId, isTransfer, user.user_name);

      if (result.success) {
        showNotification(result.message, 'success');
        // Refresh data to reflect changes
        await loadFilteredTransactions();
      } else {
        showNotification('Failed to update transfer status: ' + (result.error || 'Unknown error'), 'error');
      }
    } catch (error) {
      console.error('Error updating transfer status:', error);
      showNotification('Error updating transfer status', 'error');
    }
  };

  const handleExport = () => {
    if (filteredTransactions.length === 0) {
      showNotification('No data to export', 'warning');
      return;
    }

    const filename = `transactions-filtered-${selectedDate}-to-${endDate}.csv`;

    const csvData = filteredTransactions.map(item => ({
      'Transaction Date': item.transaction_date,
      'Merchant VAT': item.merchant_vat,
      'Merchant Name': item.merchant_name,
      'Channel Type': item.channel_type,
      'Transaction Count': item.transaction_count,
      'Total Amount': item.total_amount.toFixed(2),
      'MDR Amount': item.mdr_amount.toFixed(2),
      'VAT Amount': item.vat_amount.toFixed(2),
      'Final Net Amount': item.final_net_amount.toFixed(2),
      'Transfer Status': item.is_transfer === 1 ? 'Transferred' : 'Pending'
    }));

    const csvContent = [
      Object.keys(csvData[0]).join(','),
      ...csvData.map(row => Object.values(row).join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    link.click();
    URL.revokeObjectURL(url);

    showNotification('Data exported successfully', 'success');
  };

  const handlePrint = () => {
    window.print();
  };

  // Format currency safely
  const formatCurrency = (amount: number | null | undefined) => {
    if (amount === null || amount === undefined || isNaN(amount)) {
      return new Intl.NumberFormat('th-TH', {
        style: 'currency',
        currency: 'THB',
        minimumFractionDigits: 2
      }).format(0);
    }
    return new Intl.NumberFormat('th-TH', {
      style: 'currency',
      currency: 'THB',
      minimumFractionDigits: 2
    }).format(amount);
  };

  // Format date for table display (date only)
  const formatDate = (date: Date | string | null | undefined) => {
    if (!date) return '';
    try {
      if (date instanceof Date) {
        return date.toLocaleDateString();
      }
      if (typeof date === 'string') {
        // If it's already a string in YYYY-MM-DD format, return as is
        if (/^\d{4}-\d{2}-\d{2}$/.test(date)) {
          return date;
        }
        return new Date(date).toLocaleDateString();
      }
      return String(date);
    } catch (error) {
      console.error('Error formatting date:', error);
      return String(date);
    }
  };

  // Load initial data
  useEffect(() => {
    loadFilteredTransactions();
  }, []);

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="bg-green-600 text-white p-4 rounded-t-lg">
          <div className="flex items-center justify-between">
            <h1 className="text-xl font-bold">Transaction Summary Filter</h1>
            <button
              onClick={() => navigate('/transaction-summary')}
              className="bg-white text-green-600 px-3 py-1 rounded text-sm font-medium hover:bg-gray-50"
            >
              ← Back to Summary
            </button>
          </div>
        </div>

        {/* Filters Section */}
        <div className="bg-white p-4 border-x border-gray-300">
          <div className="flex flex-wrap gap-4 items-center p-3 bg-gray-50 rounded">
            <div className="flex items-center gap-2">
              <label className="text-sm font-medium text-gray-700">Start Date:</label>
              <input
                type="date"
                value={selectedDate}
                onChange={(e) => setSelectedDate(e.target.value)}
                className="px-2 py-1 border border-gray-300 rounded text-sm"
              />
            </div>
            <div className="flex items-center gap-2">
              <label className="text-sm font-medium text-gray-700">End Date:</label>
              <input
                type="date"
                value={endDate}
                onChange={(e) => setEndDate(e.target.value)}
                className="px-2 py-1 border border-gray-300 rounded text-sm"
              />
            </div>
            <div className="flex items-center gap-2">
              <label className="text-sm font-medium text-gray-700">Merchant VAT:</label>
              <input
                type="text"
                value={merchantVat}
                onChange={(e) => setMerchantVat(e.target.value)}
                placeholder="Enter VAT number"
                className="px-2 py-1 border border-gray-300 rounded text-sm w-40"
              />
            </div>
            <div className="flex items-center gap-2">
              <label className="text-sm font-medium text-gray-700">Channel:</label>
              <select
                value={channelType}
                onChange={(e) => setChannelType(e.target.value)}
                className="px-2 py-1 border border-gray-300 rounded text-sm"
              >
                <option value="">All Channels</option>
                <option value="WeChat">WeChat</option>
                <option value="UNIPAY">UNIPAY</option>
              </select>
            </div>
            <div className="flex items-center gap-2">
              <label className="text-sm font-medium text-gray-700">Transfer Status:</label>
              <select
                value={transferFilter === undefined ? '' : transferFilter.toString()}
                onChange={(e) => setTransferFilter(e.target.value === '' ? undefined : parseInt(e.target.value))}
                className="px-2 py-1 border border-gray-300 rounded text-sm"
              >
                <option value="">All</option>
                <option value="0">Pending</option>
                <option value="1">Transferred</option>
              </select>
            </div>
            
            {/* Filter action buttons */}
            <div className="flex items-center gap-2 ml-4">
              <button
                onClick={handleSearchFiltered}
                disabled={filteredLoading}
                className="px-3 py-1 text-sm font-medium text-white bg-green-600 hover:bg-green-700 rounded disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {filteredLoading ? 'Searching...' : 'Search'}
              </button>
              <button
                onClick={handleClearFilters}
                className="px-3 py-1 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 border border-gray-300 rounded"
              >
                Clear
              </button>
              <button
                onClick={handleExport}
                disabled={filteredTransactions.length === 0}
                className="px-3 py-1 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Export CSV
              </button>
              <button
                onClick={handlePrint}
                className="px-3 py-1 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 border border-gray-300 rounded"
              >
                Print
              </button>
            </div>
          </div>
        </div>

        {/* Summary Section */}
        <div className="bg-white p-4 border-x border-gray-300">
          <div className="bg-green-100 p-3 rounded">
            <div className="text-sm text-green-700 flex items-center gap-2">
              Filtered Transactions
              {filteredLoading && (
                <div className="animate-spin rounded-full h-4 w-4 border-2 border-green-600 border-t-transparent"></div>
              )}
            </div>
            <div className="text-lg font-bold text-green-800">
              {filteredTransactions.length} records
            </div>
            <div className="text-sm text-green-600">
              Total: {formatCurrency(filteredTransactions.reduce((sum, item) => Number(sum) + Number(item.final_net_amount), 0))}
            </div>
          </div>
        </div>

        {/* Content Area */}
        <div className="bg-white border border-gray-300 rounded-b-lg overflow-hidden">
          <div className="p-4">
            <div className="mb-4 bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-2">
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                <h3 className="text-lg font-semibold text-green-800">
                  Filtered Transactions
                </h3>
                {filteredLoading && (
                  <div className="animate-spin rounded-full h-5 w-5 border-2 border-green-600 border-t-transparent"></div>
                )}
              </div>
              <p className="text-sm text-green-700">
                Showing transactions based on your filter criteria. Total records: <strong>{filteredTransactions.length}</strong>
              </p>
              <div className="text-xs text-green-600 mt-1">
                Date Range: {selectedDate} to {endDate}
                {merchantVat && ` | Merchant VAT: ${merchantVat}`}
                {channelType && ` | Channel: ${channelType}`}
                {transferFilter !== undefined && ` | Status: ${transferFilter === 1 ? 'Transferred' : 'Pending'}`}
              </div>
            </div>

            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-green-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-green-700 uppercase tracking-wider">
                      <input
                        type="checkbox"
                        checked={filteredTransactions.length > 0 && selectedFilteredIds.size === filteredTransactions.length}
                        onChange={(e) => handleSelectAllFiltered(e.target.checked)}
                        className="rounded border-green-300 text-green-600 focus:ring-green-500"
                      />
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-green-700 uppercase tracking-wider">
                      Date
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-green-700 uppercase tracking-wider">
                      Merchant VAT
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-green-700 uppercase tracking-wider">
                      Merchant Name
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-green-700 uppercase tracking-wider">
                      Channel
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-green-700 uppercase tracking-wider">
                      Count
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-green-700 uppercase tracking-wider">
                      Total Amount
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-green-700 uppercase tracking-wider">
                      Final Net Amount
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-green-700 uppercase tracking-wider">
                      Transfer Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-green-700 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredLoading && filteredTransactions.length === 0 && (
                    [...Array(5)].map((_, index) => (
                      <tr key={index} className={index % 2 === 0 ? 'bg-white' : 'bg-green-50'}>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="animate-pulse bg-green-200 h-4 w-4 rounded"></div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="animate-pulse bg-green-200 h-4 w-20 rounded"></div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="animate-pulse bg-green-200 h-4 w-24 rounded"></div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="animate-pulse bg-green-200 h-4 w-32 rounded"></div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="animate-pulse bg-green-200 h-4 w-16 rounded"></div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="animate-pulse bg-green-200 h-4 w-12 rounded"></div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="animate-pulse bg-green-200 h-4 w-20 rounded"></div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="animate-pulse bg-green-200 h-4 w-20 rounded"></div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="animate-pulse bg-green-200 h-4 w-16 rounded"></div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="animate-pulse bg-green-200 h-4 w-16 rounded"></div>
                        </td>
                      </tr>
                    ))
                  )}
                  {filteredTransactions.map((item, index) => (
                    <tr key={item.id} className={index % 2 === 0 ? 'bg-white' : 'bg-green-50'}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <input
                          type="checkbox"
                          checked={selectedFilteredIds.has(item.id)}
                          onChange={(e) => handleSelectFiltered(item.id, e.target.checked)}
                          className="rounded border-green-300 text-green-600 focus:ring-green-500"
                        />
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {formatDate(item.transaction_date)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm">
                        <span className={item.total_amount < 0 ? 'text-red-600 font-semibold' : 'text-gray-900'}>
                          {item.merchant_vat}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm">
                        <span className={item.total_amount < 0 ? 'text-red-600 font-semibold' : 'text-gray-900'}>
                          {item.merchant_name}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm">
                        <span className={item.total_amount < 0 ? 'text-red-600 font-semibold' : 'text-gray-900'}>
                          {item.channel_type}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm">
                        <span className={item.transaction_count < 0 ? 'text-red-600 font-semibold' : 'text-gray-900'}>
                          {item.transaction_count}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm">
                        <span className={item.total_amount < 0 ? 'text-red-600 font-semibold' : 'text-gray-900'}>
                          {formatCurrency(item.total_amount)}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-semibold">
                        <span className={item.final_net_amount < 0 ? 'text-red-600' : 'text-green-800'}>
                          {formatCurrency(item.final_net_amount)}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          item.is_transfer === 1 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                        }`}>
                          {item.is_transfer === 1 ? 'Transferred' : 'Pending'}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <RoleBasedComponent requiredPermission="canCreate">
                          {item.is_transfer === 0 ? (
                            <button
                              onClick={() => updateTransferStatus(item.id, 1)}
                              className="text-green-600 hover:text-green-900 bg-green-100 hover:bg-green-200 px-3 py-1 rounded text-xs font-medium"
                            >
                              Mark Transferred
                            </button>
                          ) : (
                            <button
                              onClick={() => updateTransferStatus(item.id, 0)}
                              className="text-red-600 hover:text-red-900 bg-red-100 hover:bg-red-200 px-3 py-1 rounded text-xs font-medium"
                            >
                              Mark Pending
                            </button>
                          )}
                        </RoleBasedComponent>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
              {filteredTransactions.length === 0 && !filteredLoading && (
                <div className="text-center py-8 text-gray-500">
                  <div className="text-green-600 text-lg mb-2">🔍</div>
                  <div>No transactions found matching your filter criteria</div>
                  <div className="text-sm text-gray-400 mt-2">Try adjusting your filters and search again</div>
                </div>
              )}
            </div>

            {/* Summary for filtered transactions */}
            {filteredTransactions.length > 0 && (
              <div className="mt-4 bg-green-50 border border-green-200 rounded-lg p-4">
                <h4 className="text-sm font-semibold text-green-800 mb-2">Filtered Results Summary</h4>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
                  <div>
                    <span className="text-green-700">Total Records:</span>
                    <span className="font-semibold text-green-900 ml-2">{filteredTransactions.length}</span>
                  </div>
                  <div>
                    <span className="text-green-700">Total Amount:</span>
                    <span className="font-semibold text-green-900 ml-2">
                      {formatCurrency(filteredTransactions.reduce((sum, item) => Number(sum) + Number(item.final_net_amount), 0))}
                    </span>
                  </div>
                  <div>
                    <span className="text-green-700">Transferred:</span>
                    <span className="font-semibold text-green-900 ml-2">
                      {filteredTransactions.filter(item => item.is_transfer === 1).length}
                    </span>
                  </div>
                  <div>
                    <span className="text-green-700">Pending:</span>
                    <span className="font-semibold text-green-900 ml-2">
                      {filteredTransactions.filter(item => item.is_transfer === 0).length}
                    </span>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
