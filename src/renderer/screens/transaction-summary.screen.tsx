import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '../components/button';
import { useAuth } from '../contexts/AuthContext';
import { useNotification } from '../contexts/NotificationContext';
import { safeIpcInvoke } from '../utils/electron';
import { RoleBasedComponent } from '../components/RoleBasedComponent';

// Types for Transaction Summary using only detail table
interface TransactionSummaryDetailItem {
  id: number;
  merchant_vat: string;
  merchant_name: string;
  transaction_date: string;
  channel_type: string;
  transaction_count: number;
  total_amount: number;
  mdr_rate: number;
  mdr_amount: number;
  vat_percentage: number;
  vat_amount: number;
  net_amount: number;
  withholding_tax_rate: number;
  withhold_tax: number;
  transfer_fee: number;
  reimbursement_fee: number;
  service_fee: number;
  final_net_amount: number;
  cup_business_tax_fee: number;
  is_transfer: number;
  create_by: string;
  create_dt: string;
  update_by: string;
  update_dt: string;
}



export function TransactionSummaryScreen() {
  const { user } = useAuth();
  const { showNotification } = useNotification();
  const navigate = useNavigate();

  // State variables for new workflow
  const [todayTransactions, setTodayTransactions] = useState<TransactionSummaryDetailItem[]>([]);
  const [pendingTransactions, setPendingTransactions] = useState<TransactionSummaryDetailItem[]>([]);

  // Loading states
  const [todayLoading, setTodayLoading] = useState(false);
  const [pendingLoading, setPendingLoading] = useState(false);

  // Active tab state
  const [activeTab, setActiveTab] = useState<'today' | 'pending'>('today');
  const [lastRefresh, setLastRefresh] = useState<Date | null>(null);

  // Selection states for bulk operations
  const [selectedTodayIds, setSelectedTodayIds] = useState<Set<number>>(new Set());
  const [selectedPendingIds, setSelectedPendingIds] = useState<Set<number>>(new Set());
  const [bulkApproving, setBulkApproving] = useState(false);

  // Confirmation modal state
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [confirmationData, setConfirmationData] = useState<{
    count: number;
    tab: string;
    selectedIds: Set<number>;
  } | null>(null);

  // Load today's transactions (current date)
  const loadTodayTransactions = async () => {
    setTodayLoading(true);
    try {
      const today = new Date().toISOString().split('T')[0];
      console.log('🔍 Loading today\'s transactions for:', today);

      const result = await safeIpcInvoke('get-transaction-summary-today', {
        startDate: today,
        endDate: today,
        page: 1,
        pageSize: 1000
      });

      if (result.success) {
        setTodayTransactions(result.data || []);
        console.log(`📋 Loaded ${result.data?.length || 0} today's transactions`);
      } else {
        showNotification('Failed to load today\'s transactions: ' + (result.error || 'Unknown error'), 'error');
        setTodayTransactions([]);
      }
    } catch (error) {
      console.error('Error loading today\'s transactions:', error);
      showNotification('Error loading today\'s transactions', 'error');
      setTodayTransactions([]);
    } finally {
      setTodayLoading(false);
      setLastRefresh(new Date());
    }
  };

  // Load all pending transactions (across all dates)
  const loadPendingTransactions = async () => {
    setPendingLoading(true);
    try {
      console.log('🔍 Loading all pending transactions...');

      const result = await safeIpcInvoke('get-transaction-summary-details', {
        isTransfer: 0, // Only pending transactions
        page: 1,
        pageSize: 1000
      });

      if (result.success) {
        setPendingTransactions(result.data || []);
        console.log(`📋 Loaded ${result.data?.length || 0} pending transactions`);
      } else {
        showNotification('Failed to load pending transactions: ' + (result.error || 'Unknown error'), 'error');
        setPendingTransactions([]);
      }
    } catch (error) {
      console.error('Error loading pending transactions:', error);
      showNotification('Error loading pending transactions', 'error');
      setPendingTransactions([]);
    } finally {
      setPendingLoading(false);
      setLastRefresh(new Date());
    }
  };

  // Event handlers for new workflow
  const handleRefreshAll = async () => {
    try {
      // Clear selections when refreshing
      setSelectedTodayIds(new Set());
      setSelectedPendingIds(new Set());

      await Promise.all([
        loadTodayTransactions(),
        loadPendingTransactions()
      ]);
      showNotification('All transaction data refreshed', 'success');
    } catch (error) {
      console.error('Error refreshing all data:', error);
      showNotification('Error refreshing data', 'error');
    }
  };

  // Selection helper functions
  const handleSelectToday = (id: number, checked: boolean) => {
    const newSelected = new Set(selectedTodayIds);
    if (checked) {
      newSelected.add(id);
    } else {
      newSelected.delete(id);
    }
    setSelectedTodayIds(newSelected);
  };

  const handleSelectAllToday = (checked: boolean) => {
    if (checked) {
      const allIds = new Set(todayTransactions.map(item => item.id));
      setSelectedTodayIds(allIds);
    } else {
      setSelectedTodayIds(new Set());
    }
  };

  const handleSelectPending = (id: number, checked: boolean) => {
    const newSelected = new Set(selectedPendingIds);
    if (checked) {
      newSelected.add(id);
    } else {
      newSelected.delete(id);
    }
    setSelectedPendingIds(newSelected);
  };

  const handleSelectAllPending = (checked: boolean) => {
    if (checked) {
      const allIds = new Set(pendingTransactions.map(item => item.id));
      setSelectedPendingIds(allIds);
    } else {
      setSelectedPendingIds(new Set());
    }
  };

  const updateTransferStatus = async (detailId: number, isTransfer: number) => {
    if (!user?.user_name) {
      showNotification('User not authenticated', 'error');
      return;
    }

    try {
      const result = await safeIpcInvoke('update-transfer-status', detailId, isTransfer, user.user_name);

      if (result.success) {
        showNotification(result.message, 'success');
        // Refresh all data to reflect changes
        await handleRefreshAll();
      } else {
        showNotification('Failed to update transfer status: ' + (result.error || 'Unknown error'), 'error');
      }
    } catch (error) {
      console.error('Error updating transfer status:', error);
      showNotification('Error updating transfer status', 'error');
    }
  };

  // Bulk approval function with confirmation
  const handleBulkApprove = async () => {
    if (!user?.user_name) {
      showNotification('User not authenticated', 'error');
      return;
    }

    const selectedIds = activeTab === 'today' ? selectedTodayIds : selectedPendingIds;

    if (selectedIds.size === 0) {
      showNotification('Please select transactions to approve', 'warning');
      return;
    }

    // Show confirmation modal
    setConfirmationData({
      count: selectedIds.size,
      tab: activeTab,
      selectedIds: new Set(selectedIds)
    });
    setShowConfirmModal(true);
  };

  // Execute bulk approval after confirmation
  const executeBulkApproval = async () => {
    if (!confirmationData || !user?.user_name) {
      return;
    }

    setBulkApproving(true);
    setShowConfirmModal(false);

    try {
      const promises = Array.from(confirmationData.selectedIds).map(id =>
        safeIpcInvoke('update-transfer-status', id, 1, user.user_name)
      );

      const results = await Promise.all(promises);
      const successCount = results.filter(result => result.success).length;
      const failCount = results.length - successCount;

      if (successCount > 0) {
        showNotification(`Successfully approved ${successCount} transaction(s)${failCount > 0 ? `, ${failCount} failed` : ''}`, 'success');

        // Clear selections and refresh data
        if (confirmationData.tab === 'today') {
          setSelectedTodayIds(new Set());
        } else {
          setSelectedPendingIds(new Set());
        }

        await handleRefreshAll();
      } else {
        showNotification('Failed to approve any transactions', 'error');
      }
    } catch (error) {
      console.error('Error in bulk approval:', error);
      showNotification('Error during bulk approval', 'error');
    } finally {
      setBulkApproving(false);
      setConfirmationData(null);
    }
  };

  // Cancel bulk approval
  const cancelBulkApproval = () => {
    setShowConfirmModal(false);
    setConfirmationData(null);
  };

  const handleExport = () => {
    let dataToExport: TransactionSummaryDetailItem[] = [];
    let filename = '';

    switch (activeTab) {
      case 'today':
        dataToExport = todayTransactions;
        filename = `transactions-today-${new Date().toISOString().split('T')[0]}.csv`;
        break;
      case 'pending':
        dataToExport = pendingTransactions;
        filename = `transactions-pending-${new Date().toISOString().split('T')[0]}.csv`;
        break;
      default:
        dataToExport = [];
    }

    if (dataToExport.length === 0) {
      showNotification('No data to export', 'warning');
      return;
    }

    const csvData = dataToExport.map(item => ({
      'Transaction Date': item.transaction_date,
      'Merchant VAT': item.merchant_vat,
      'Merchant Name': item.merchant_name,
      'Channel Type': item.channel_type,
      'Transaction Count': item.transaction_count,
      'Total Amount': item.total_amount.toFixed(2),
      'MDR Amount': item.mdr_amount.toFixed(2),
      'VAT Amount': item.vat_amount.toFixed(2),
      'Final Net Amount': item.final_net_amount.toFixed(2),
      'Transfer Status': item.is_transfer === 1 ? 'Transferred' : 'Pending'
    }));

    const csvContent = [
      Object.keys(csvData[0]).join(','),
      ...csvData.map(row => Object.values(row).join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    link.click();
    URL.revokeObjectURL(url);

    showNotification('Data exported successfully', 'success');
  };

  const handlePrint = () => {
    window.print();
  };

  // Format currency safely
  const formatCurrency = (amount: number | null | undefined) => {
    if (amount === null || amount === undefined || isNaN(amount)) {
      return new Intl.NumberFormat('th-TH', {
        style: 'currency',
        currency: 'THB',
        minimumFractionDigits: 2
      }).format(0);
    }
    return new Intl.NumberFormat('th-TH', {
      style: 'currency',
      currency: 'THB',
      minimumFractionDigits: 2
    }).format(amount);
  };

  // Format date safely for display
  const formatDateTime = (date: Date | string | null | undefined) => {
    if (!date) return '';
    try {
      if (date instanceof Date) {
        return date.toLocaleTimeString();
      }
      if (typeof date === 'string') {
        return new Date(date).toLocaleTimeString();
      }
      return String(date);
    } catch (error) {
      console.error('Error formatting date:', error);
      return String(date);
    }
  };

  // Format date for table display (date only)
  const formatDate = (date: Date | string | null | undefined) => {
    if (!date) return '';
    try {
      if (date instanceof Date) {
        return date.toLocaleDateString();
      }
      if (typeof date === 'string') {
        // If it's already a string in YYYY-MM-DD format, return as is
        if (/^\d{4}-\d{2}-\d{2}$/.test(date)) {
          return date;
        }
        return new Date(date).toLocaleDateString();
      }
      return String(date);
    } catch (error) {
      console.error('Error formatting date:', error);
      return String(date);
    }
  };

  // Load initial data for all tabs
  useEffect(() => {
    const initializeData = async () => {
      console.log('🚀 Initializing transaction summary data...');

      try {
        // Load all data concurrently for the new workflow
        await Promise.all([
          loadTodayTransactions(),
          loadPendingTransactions()
        ]);

        console.log('✅ Initial data loading complete');
      } catch (error) {
        console.error('❌ Error during initialization:', error);
        showNotification('Error loading initial data', 'error');
      }
    };

    initializeData();
  }, []); // Empty dependency array for one-time initialization

  // Handle tab changes to ensure data is fresh
  useEffect(() => {
    if (activeTab === 'today' && todayTransactions.length === 0 && !todayLoading) {
      console.log('📋 Switching to today tab - loading data if empty');
      loadTodayTransactions();
    }
    if (activeTab === 'pending' && pendingTransactions.length === 0 && !pendingLoading) {
      console.log('📋 Switching to pending tab - loading data if empty');
      loadPendingTransactions();
    }
  }, [activeTab]);

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="bg-blue-600 text-white p-4 rounded-t-lg">
          <h1 className="text-xl font-bold">Transaction Summary</h1>
        </div>

        {/* Controls */}
        <div className="bg-white p-4 border-x border-gray-300">
          <div className="flex flex-wrap gap-4 items-center">
            {/* Navigate to Filter Screen Button */}
            <Button
              onClick={() => navigate('/transaction-summary-filter')}
              className="bg-green-600 hover:bg-green-700 text-white"
            >
              🔍 Filter Transactions
            </Button>

            {/* Bulk Approve Button - only show for Today and Pending tabs */}
            {(activeTab === 'today' || activeTab === 'pending') && (
              <RoleBasedComponent requiredPermission="canCreate">
                <Button
                  onClick={handleBulkApprove}
                  disabled={bulkApproving || (activeTab === 'today' ? selectedTodayIds.size === 0 : selectedPendingIds.size === 0)}
                  className="bg-orange-600 hover:bg-orange-700 text-white"
                >
                  {bulkApproving ? 'Approving...' : `Approve Selected (${activeTab === 'today' ? selectedTodayIds.size : selectedPendingIds.size})`}
                </Button>
              </RoleBasedComponent>
            )}
          </div>
        </div>

        {/* Summary Section */}
        <div className="bg-white p-4 border-x border-gray-300">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Today's Transactions */}
            <div className="bg-blue-100 p-3 rounded">
              <div className="text-sm text-blue-700 flex items-center gap-2">
                Today's Transactions
                {todayLoading && (
                  <div className="animate-spin rounded-full h-4 w-4 border-2 border-blue-600 border-t-transparent"></div>
                )}
              </div>
              <div className="text-lg font-bold text-blue-800">
                {todayTransactions.length} records
              </div>
              <div className="text-sm text-blue-600">
                Total: {formatCurrency(todayTransactions.reduce((sum, item) => Number(sum) + Number(item.final_net_amount), 0))}
              </div>
            </div>

            {/* Pending Transactions */}
            <div className="bg-orange-100 p-3 rounded">
              <div className="text-sm text-orange-700 flex items-center gap-2">
                Pending Transactions
                {pendingLoading && (
                  <div className="animate-spin rounded-full h-4 w-4 border-2 border-orange-600 border-t-transparent"></div>
                )}
              </div>
              <div className="text-lg font-bold text-orange-800">
                {pendingTransactions.length} records
              </div>
              <div className="text-sm text-orange-600">
                Total: {formatCurrency(pendingTransactions.reduce((sum, item) => Number(sum) + Number(item.final_net_amount), 0))}
              </div>
            </div>

          </div>
        </div>

        {/* Tab Navigation */}
        <div className="bg-white border-x border-gray-300">
          <div className="flex border-b overflow-x-auto">
            <button
              onClick={() => setActiveTab('today')}
              className={`px-4 py-2 font-medium whitespace-nowrap ${
                activeTab === 'today'
                  ? 'border-b-2 border-blue-500 text-blue-600'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              <span className="flex items-center gap-2">
                📅 List Transaction Today
                <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                  {todayTransactions.length}
                </span>
              </span>
            </button>
            <button
              onClick={() => setActiveTab('pending')}
              className={`px-4 py-2 font-medium whitespace-nowrap ${
                activeTab === 'pending'
                  ? 'border-b-2 border-orange-500 text-orange-600'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              <span className="flex items-center gap-2">
                ⏳ List Transaction Pending
                <span className="bg-orange-100 text-orange-800 text-xs px-2 py-1 rounded-full">
                  {pendingTransactions.length}
                </span>
              </span>
            </button>
          </div>
        </div>

        {/* Content Area */}
        <div className="bg-white border border-gray-300 rounded-b-lg overflow-hidden">
          {/* Today's Transactions Tab */}
          {activeTab === 'today' && (
            <div className="p-4">
              <div className="mb-4 bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-center gap-2 mb-2">
                  <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                  <h3 className="text-lg font-semibold text-blue-800">
                    Today's Transactions ({new Date().toLocaleDateString()})
                  </h3>
                  {todayLoading && (
                    <div className="animate-spin rounded-full h-5 w-5 border-2 border-blue-600 border-t-transparent"></div>
                  )}
                </div>
                <p className="text-sm text-blue-700">
                  Total records: <strong>{todayTransactions.length}</strong>
                </p>
                
              </div>

              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-blue-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-blue-700 uppercase tracking-wider">
                        <input
                          type="checkbox"
                          checked={todayTransactions.length > 0 && selectedTodayIds.size === todayTransactions.length}
                          onChange={(e) => handleSelectAllToday(e.target.checked)}
                          className="rounded border-blue-300 text-blue-600 focus:ring-blue-500"
                        />
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-blue-700 uppercase tracking-wider">
                        Date
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-blue-700 uppercase tracking-wider">
                        Merchant VAT
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-blue-700 uppercase tracking-wider">
                        Merchant Name
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-blue-700 uppercase tracking-wider">
                        Channel
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-blue-700 uppercase tracking-wider">
                        Count
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-blue-700 uppercase tracking-wider">
                        Total Amount
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-blue-700 uppercase tracking-wider">
                        Final Net Amount
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-blue-700 uppercase tracking-wider">
                        Transfer Status
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-blue-700 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {todayLoading && todayTransactions.length === 0 && (
                      [...Array(5)].map((_, index) => (
                        <tr key={index} className={index % 2 === 0 ? 'bg-white' : 'bg-blue-50'}>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="animate-pulse bg-blue-200 h-4 w-4 rounded"></div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="animate-pulse bg-blue-200 h-4 w-20 rounded"></div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="animate-pulse bg-blue-200 h-4 w-24 rounded"></div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="animate-pulse bg-blue-200 h-4 w-32 rounded"></div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="animate-pulse bg-blue-200 h-4 w-16 rounded"></div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="animate-pulse bg-blue-200 h-4 w-12 rounded"></div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="animate-pulse bg-blue-200 h-4 w-20 rounded"></div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="animate-pulse bg-blue-200 h-4 w-20 rounded"></div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="animate-pulse bg-blue-200 h-4 w-16 rounded"></div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="animate-pulse bg-blue-200 h-4 w-16 rounded"></div>
                          </td>
                        </tr>
                      ))
                    )}
                    {todayTransactions.map((item, index) => (
                      <tr key={item.id} className={index % 2 === 0 ? 'bg-white' : 'bg-blue-50'}>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <input
                            type="checkbox"
                            checked={selectedTodayIds.has(item.id)}
                            onChange={(e) => handleSelectToday(item.id, e.target.checked)}
                            className="rounded border-blue-300 text-blue-600 focus:ring-blue-500"
                          />
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {formatDate(item.transaction_date)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm">
                          <span className={item.total_amount < 0 ? 'text-red-600 font-semibold' : 'text-gray-900'}>
                            {item.merchant_vat}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm">
                          <span className={item.total_amount < 0 ? 'text-red-600 font-semibold' : 'text-gray-900'}>
                            {item.merchant_name}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm">
                          <span className={item.total_amount < 0 ? 'text-red-600 font-semibold' : 'text-gray-900'}>
                            {item.channel_type}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm">
                          <span className={item.transaction_count < 0 ? 'text-red-600 font-semibold' : 'text-gray-900'}>
                            {item.transaction_count}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm">
                          <span className={item.total_amount < 0 ? 'text-red-600 font-semibold' : 'text-gray-900'}>
                            {formatCurrency(item.total_amount)}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-semibold">
                          <span className={item.final_net_amount < 0 ? 'text-red-600' : 'text-blue-800'}>
                            {formatCurrency(item.final_net_amount)}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            item.is_transfer === 1 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                          }`}>
                            {item.is_transfer === 1 ? 'Transferred' : 'Pending'}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <RoleBasedComponent requiredPermission="canCreate">
                            {item.is_transfer === 0 ? (
                              <button
                                onClick={() => updateTransferStatus(item.id, 1)}
                                className="text-green-600 hover:text-green-900 bg-green-100 hover:bg-green-200 px-3 py-1 rounded text-xs font-medium"
                              >
                                Mark Transferred
                              </button>
                            ) : (
                              <button
                                onClick={() => updateTransferStatus(item.id, 0)}
                                className="text-red-600 hover:text-red-900 bg-red-100 hover:bg-red-200 px-3 py-1 rounded text-xs font-medium"
                              >
                                Mark Pending
                              </button>
                            )}
                          </RoleBasedComponent>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
                {todayTransactions.length === 0 && !todayLoading && (
                  <div className="text-center py-8 text-gray-500">
                    <div className="text-blue-600 text-lg mb-2">📅</div>
                    <div>No transactions found for today</div>
                  </div>
                )}
              </div>

              {/* Summary for today's transactions */}
              {todayTransactions.length > 0 && (
                <div className="mt-4 bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <h4 className="text-sm font-semibold text-blue-800 mb-2">Today's Summary</h4>
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <span className="text-blue-700">Total Records:</span>
                      <span className="font-semibold text-blue-900 ml-2">{todayTransactions.length}</span>
                    </div>
                    <div>
                      <span className="text-blue-700">Total Amount:</span>
                      <span className="font-semibold text-blue-900 ml-2">
                        {formatCurrency(todayTransactions.reduce((sum, item) => Number(sum) + Number(item.final_net_amount), 0))}
                      </span>
                    </div>
                    <div>
                      <span className="text-blue-700">Transferred:</span>
                      <span className="font-semibold text-blue-900 ml-2">
                        {todayTransactions.filter(item => item.is_transfer === 1).length}
                      </span>
                    </div>
                    <div>
                      <span className="text-blue-700">Pending:</span>
                      <span className="font-semibold text-blue-900 ml-2">
                        {todayTransactions.filter(item => item.is_transfer === 0).length}
                      </span>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}
          {/* Pending Transactions Tab */}
          {activeTab === 'pending' && (
            <div className="p-4">
              <div className="mb-4 bg-orange-50 border border-orange-200 rounded-lg p-4">
                <div className="flex items-center gap-2 mb-2">
                  <div className="w-3 h-3 bg-orange-500 rounded-full animate-pulse"></div>
                  <h3 className="text-lg font-semibold text-orange-800">
                    All Pending Transactions
                  </h3>
                  {pendingLoading && (
                    <div className="animate-spin rounded-full h-5 w-5 border-2 border-orange-600 border-t-transparent"></div>
                  )}
                </div>
                <p className="text-sm text-orange-700">
                  Total records: <strong>{pendingTransactions.length}</strong>
                </p>
                
              </div>

              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-orange-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-orange-700 uppercase tracking-wider">
                        <input
                          type="checkbox"
                          checked={pendingTransactions.length > 0 && selectedPendingIds.size === pendingTransactions.length}
                          onChange={(e) => handleSelectAllPending(e.target.checked)}
                          className="rounded border-orange-300 text-orange-600 focus:ring-orange-500"
                        />
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-orange-700 uppercase tracking-wider">
                        Date
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-orange-700 uppercase tracking-wider">
                        Merchant VAT
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-orange-700 uppercase tracking-wider">
                        Merchant Name
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-orange-700 uppercase tracking-wider">
                        Channel
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-orange-700 uppercase tracking-wider">
                        Count
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-orange-700 uppercase tracking-wider">
                        Total Amount
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-orange-700 uppercase tracking-wider">
                        Final Net Amount
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-orange-700 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {pendingLoading && pendingTransactions.length === 0 && (
                      [...Array(5)].map((_, index) => (
                        <tr key={index} className={index % 2 === 0 ? 'bg-white' : 'bg-orange-50'}>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="animate-pulse bg-orange-200 h-4 w-4 rounded"></div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="animate-pulse bg-orange-200 h-4 w-20 rounded"></div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="animate-pulse bg-orange-200 h-4 w-24 rounded"></div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="animate-pulse bg-orange-200 h-4 w-32 rounded"></div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="animate-pulse bg-orange-200 h-4 w-16 rounded"></div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="animate-pulse bg-orange-200 h-4 w-12 rounded"></div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="animate-pulse bg-orange-200 h-4 w-20 rounded"></div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="animate-pulse bg-orange-200 h-4 w-20 rounded"></div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="animate-pulse bg-orange-200 h-4 w-16 rounded"></div>
                          </td>
                        </tr>
                      ))
                    )}
                    {pendingTransactions.map((item, index) => (
                      <tr key={item.id} className={index % 2 === 0 ? 'bg-white' : 'bg-orange-50'}>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <input
                            type="checkbox"
                            checked={selectedPendingIds.has(item.id)}
                            onChange={(e) => handleSelectPending(item.id, e.target.checked)}
                            className="rounded border-orange-300 text-orange-600 focus:ring-orange-500"
                          />
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {formatDate(item.transaction_date)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm">
                          <span className={item.total_amount < 0 ? 'text-red-600 font-semibold' : 'text-gray-900'}>
                            {item.merchant_vat}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm">
                          <span className={item.total_amount < 0 ? 'text-red-600 font-semibold' : 'text-gray-900'}>
                            {item.merchant_name}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm">
                          <span className={item.total_amount < 0 ? 'text-red-600 font-semibold' : 'text-gray-900'}>
                            {item.channel_type}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm">
                          <span className={item.transaction_count < 0 ? 'text-red-600 font-semibold' : 'text-gray-900'}>
                            {item.transaction_count}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm">
                          <span className={item.total_amount < 0 ? 'text-red-600 font-semibold' : 'text-gray-900'}>
                            {formatCurrency(item.total_amount)}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-semibold">
                          <span className={item.final_net_amount < 0 ? 'text-red-600' : 'text-orange-800'}>
                            {formatCurrency(item.final_net_amount)}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <RoleBasedComponent requiredPermission="canCreate">
                            <button
                              onClick={() => updateTransferStatus(item.id, 1)}
                              className="text-green-600 hover:text-green-900 bg-green-100 hover:bg-green-200 px-3 py-1 rounded text-xs font-medium"
                            >
                              Mark Transferred
                            </button>
                          </RoleBasedComponent>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
                {pendingTransactions.length === 0 && !pendingLoading && (
                  <div className="text-center py-8 text-gray-500">
                    <div className="text-green-600 text-lg mb-2">🎉</div>
                    <div>No pending transactions found - All transfers are complete!</div>
                  </div>
                )}
              </div>

              {/* Summary for pending transactions */}
              {pendingTransactions.length > 0 && (
                <div className="mt-4 bg-orange-50 border border-orange-200 rounded-lg p-4">
                  <h4 className="text-sm font-semibold text-orange-800 mb-2">Pending Transactions Summary</h4>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div>
                      <span className="text-orange-700">Total Records:</span>
                      <span className="font-semibold text-orange-900 ml-2">{pendingTransactions.length}</span>
                    </div>
                    <div>
                      <span className="text-orange-700">Total Amount:</span>
                      <span className="font-semibold text-orange-900 ml-2">
                        {formatCurrency(pendingTransactions.reduce((sum, item) => Number(sum) + Number(item.final_net_amount), 0))}
                      </span>
                    </div>
                    <div>
                      <span className="text-orange-700">Unique Merchants:</span>
                      <span className="font-semibold text-orange-900 ml-2">
                        {new Set(pendingTransactions.map(item => item.merchant_vat)).size}
                      </span>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}

        </div>

        {/* Confirmation Modal */}
        {showConfirmModal && confirmationData && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4 shadow-xl">
              <div className="flex items-center mb-4">
                <div className="flex-shrink-0">
                  <div className="w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center">
                    <svg className="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
                  </div>
                </div>
                <div className="ml-4">
                  <h3 className="text-lg font-medium text-gray-900">
                    Confirm Bulk Approval
                  </h3>
                </div>
              </div>

              <div className="mb-6">
                <p className="text-sm text-gray-600 mb-3">
                  You are about to approve <strong>{confirmationData.count}</strong> selected transaction{confirmationData.count > 1 ? 's' : ''} from{' '}
                  <strong>{confirmationData.tab === 'today' ? "Today's Transactions" : "Pending Transactions"}</strong>.
                </p>
                <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <svg className="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div className="ml-3">
                      <p className="text-sm text-yellow-700">
                        <strong>Warning:</strong> This action will update the transfer status to "Transferred" and cannot be easily undone.
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex justify-end space-x-3">
                <button
                  onClick={cancelBulkApproval}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
                >
                  Cancel
                </button>
                <button
                  onClick={executeBulkApproval}
                  disabled={bulkApproving}
                  className="px-4 py-2 text-sm font-medium text-white bg-orange-600 border border-transparent rounded-md hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {bulkApproving ? 'Approving...' : `Approve ${confirmationData.count} Transaction${confirmationData.count > 1 ? 's' : ''}`}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

