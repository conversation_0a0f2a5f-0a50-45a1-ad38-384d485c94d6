import { safeIpcInvoke } from './electron';

/**
 * Enhanced IPC utilities that automatically inject user information
 */

interface UserInfo {
  user_name: string;
}

/**
 * Create a record with automatic create_by injection
 */
export async function createRecord<T extends Record<string, any>>(
  channel: string,
  data: T,
  currentUser: UserInfo | null
): Promise<any> {
  const dataWithUser = {
    ...data,
    create_by: currentUser?.user_name || 'SYSTEM'
  };
  
  return safeIpcInvoke(channel, dataWithUser);
}

/**
 * Update a record with automatic update_by injection
 */
export async function updateRecord<T extends Record<string, any>>(
  channel: string,
  id: number | string,
  data: T,
  currentUser: UserInfo | null
): Promise<any> {
  const dataWithUser = {
    ...data,
    update_by: currentUser?.user_name || 'SYSTEM'
  };
  
  return safeIpcInvoke(channel, id, dataWithUser);
}

/**
 * Create or update a record with automatic user field injection
 */
export async function saveRecord<T extends Record<string, any>>(
  createChannel: string,
  updateChannel: string,
  data: T,
  currentUser: UserInfo | null,
  isUpdate: boolean = false,
  id?: number | string
): Promise<any> {
  if (isUpdate && id !== undefined) {
    return updateRecord(updateChannel, id, data, currentUser);
  } else {
    return createRecord(createChannel, data, currentUser);
  }
}

/**
 * Batch create records with automatic create_by injection
 */
export async function createRecords<T extends Record<string, any>>(
  channel: string,
  records: T[],
  currentUser: UserInfo | null
): Promise<any> {
  const recordsWithUser = records.map(record => ({
    ...record,
    create_by: currentUser?.user_name || 'SYSTEM'
  }));
  
  return safeIpcInvoke(channel, recordsWithUser);
}

/**
 * Batch update records with automatic update_by injection
 */
export async function updateRecords<T extends Record<string, any>>(
  channel: string,
  records: Array<T & { id: number | string }>,
  currentUser: UserInfo | null
): Promise<any> {
  const recordsWithUser = records.map(record => ({
    ...record,
    update_by: currentUser?.user_name || 'SYSTEM'
  }));
  
  return safeIpcInvoke(channel, recordsWithUser);
}

/**
 * Generic IPC call with automatic user field injection based on operation type
 */
export async function ipcWithUser<T extends Record<string, any>>(
  channel: string,
  data: T,
  currentUser: UserInfo | null,
  operation: 'create' | 'update' = 'create',
  ...additionalArgs: any[]
): Promise<any> {
  let dataWithUser: T & { create_by?: string; update_by?: string };
  
  if (operation === 'create') {
    dataWithUser = {
      ...data,
      create_by: currentUser?.user_name || 'SYSTEM'
    };
  } else {
    dataWithUser = {
      ...data,
      update_by: currentUser?.user_name || 'SYSTEM'
    };
  }
  
  return safeIpcInvoke(channel, dataWithUser, ...additionalArgs);
}

/**
 * Hook-based IPC utilities for React components
 */
export function createIpcWithUserHook(currentUser: UserInfo | null) {
  return {
    create: <T extends Record<string, any>>(channel: string, data: T) => 
      createRecord(channel, data, currentUser),
    
    update: <T extends Record<string, any>>(channel: string, id: number | string, data: T) => 
      updateRecord(channel, id, data, currentUser),
    
    save: <T extends Record<string, any>>(
      createChannel: string, 
      updateChannel: string, 
      data: T, 
      isUpdate: boolean = false, 
      id?: number | string
    ) => 
      saveRecord(createChannel, updateChannel, data, currentUser, isUpdate, id),
    
    createBatch: <T extends Record<string, any>>(channel: string, records: T[]) => 
      createRecords(channel, records, currentUser),
    
    updateBatch: <T extends Record<string, any>>(channel: string, records: Array<T & { id: number | string }>) => 
      updateRecords(channel, records, currentUser),
    
    invoke: <T extends Record<string, any>>(
      channel: string, 
      data: T, 
      operation: 'create' | 'update' = 'create', 
      ...additionalArgs: any[]
    ) => 
      ipcWithUser(channel, data, currentUser, operation, ...additionalArgs)
  };
}
