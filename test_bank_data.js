// Test script to check bank data and table existence
// Run this to debug bank subtotal issues

const { Pool } = require('pg');

// Database connection (update with your actual connection details)
const pool = new Pool({
  host: 'ep-dawn-fog-a1jk7z7f-pooler.ap-southeast-1.aws.neon.tech',
  database: 'neondb',
  user: 'neondb_owner',
  password: 'npg_v1aKnJdNXif4',
  port: 5432,
  ssl: { rejectUnauthorized: false }
});

async function testBankData() {
  const client = await pool.connect();
  
  try {
    console.log('🔍 Testing bank data and table existence...\n');

    // 1. Check if tables exist
    console.log('1. Checking table existence:');
    const tableCheck = await client.query(`
      SELECT
        EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'merchant') as merchant_exists,
        EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'merchant_bank') as merchant_bank_exists,
        EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'tmst_bank') as tmst_bank_exists,
        EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'transaction_e_pos') as transaction_exists
    `);
    console.log('Table existence:', tableCheck.rows[0]);
    console.log('');

    // 2. Check tmst_bank data
    console.log('2. Checking tmst_bank data:');
    try {
      const bankData = await client.query('SELECT bank_id, bank_code, bank_name_th, bank_name_en, active FROM tmst_bank LIMIT 5');
      console.log(`Found ${bankData.rows.length} banks:`);
      bankData.rows.forEach(bank => {
        console.log(`  - ${bank.bank_code}: ${bank.bank_name_en} (Active: ${bank.active})`);
      });
    } catch (error) {
      console.log('Error querying tmst_bank:', error.message);
    }
    console.log('');

    // 3. Check merchant_bank data
    console.log('3. Checking merchant_bank data:');
    try {
      const merchantBankData = await client.query(`
        SELECT mb.merchant_id, mb.bank_id, mb.bank_account_no, mb.active,
               b.bank_code, b.bank_name_en
        FROM merchant_bank mb
        LEFT JOIN tmst_bank b ON mb.bank_id = b.bank_id
        LIMIT 5
      `);
      console.log(`Found ${merchantBankData.rows.length} merchant-bank relationships:`);
      merchantBankData.rows.forEach(mb => {
        console.log(`  - Merchant ${mb.merchant_id} -> Bank ${mb.bank_code} (${mb.bank_name_en}) - Active: ${mb.active}`);
      });
    } catch (error) {
      console.log('Error querying merchant_bank:', error.message);
    }
    console.log('');

    // 4. Check merchant data
    console.log('4. Checking merchant data:');
    try {
      const merchantData = await client.query('SELECT merchant_id, merchant_vat, merchant_name FROM merchant LIMIT 5');
      console.log(`Found ${merchantData.rows.length} merchants:`);
      merchantData.rows.forEach(merchant => {
        console.log(`  - ${merchant.merchant_id}: ${merchant.merchant_name} (VAT: ${merchant.merchant_vat})`);
      });
    } catch (error) {
      console.log('Error querying merchant:', error.message);
    }
    console.log('');

    // 5. Check transaction VAT numbers vs merchant VAT numbers
    console.log('5. Checking transaction VAT numbers vs merchant VAT numbers:');
    try {
      const transactionVats = await client.query(`
        SELECT DISTINCT transaction_merchant_vat, transaction_merchant_name
        FROM transaction_e_pos
        WHERE transaction_trade_status IN ('success', 'refund')
        LIMIT 10
      `);
      console.log('Transaction VAT numbers:');
      transactionVats.rows.forEach(t => {
        console.log(`  - ${t.transaction_merchant_name}: ${t.transaction_merchant_vat}`);
      });

      console.log('\nMerchant VAT numbers:');
      const merchantVats = await client.query('SELECT merchant_vat, merchant_name FROM merchant LIMIT 10');
      merchantVats.rows.forEach(m => {
        console.log(`  - ${m.merchant_name}: ${m.merchant_vat}`);
      });

      console.log('\nVAT matching check:');
      for (const txn of transactionVats.rows) {
        const match = merchantVats.rows.find(m => m.merchant_vat === txn.transaction_merchant_vat);
        console.log(`  - ${txn.transaction_merchant_vat}: ${match ? '✅ MATCH' : '❌ NO MATCH'}`);
      }
    } catch (error) {
      console.log('Error checking VAT numbers:', error.message);
    }
    console.log('');

    // 6. Test the complete join query
    console.log('6. Testing complete join query:');
    try {
      const joinQuery = `
        SELECT
          t.transaction_merchant_vat,
          t.transaction_merchant_name,
          m.merchant_id,
          m.merchant_name,
          b.bank_code,
          b.bank_name_th,
          b.bank_name_en,
          mb.bank_account_no
        FROM transaction_e_pos t
        LEFT JOIN merchant m ON t.transaction_merchant_vat = m.merchant_vat
        LEFT JOIN merchant_bank mb ON m.merchant_id = mb.merchant_id AND mb.active = true
        LEFT JOIN tmst_bank b ON mb.bank_id = b.bank_id AND b.active = true
        WHERE t.transaction_trade_status IN ('success', 'refund')
        LIMIT 5
      `;

      const joinResult = await client.query(joinQuery);
      console.log(`Join query returned ${joinResult.rows.length} rows:`);
      joinResult.rows.forEach(row => {
        console.log(`  - ${row.transaction_merchant_name} (VAT: ${row.transaction_merchant_vat})`);
        console.log(`    Merchant ID: ${row.merchant_id || 'NULL'}`);
        console.log(`    Bank: ${row.bank_code || 'NULL'} - ${row.bank_name_en || 'NULL'}`);
        console.log(`    Account: ${row.bank_account_no || 'NULL'}`);
        console.log('');
      });
    } catch (error) {
      console.log('Error with join query:', error.message);
    }

  } finally {
    client.release();
  }
}

async function testTransactionSummaryQuery() {
  const client = await pool.connect();

  try {
    console.log('\n🔍 Testing transaction summary query with bank joins...\n');

    // Get some transaction file names to test with
    const fileNames = await client.query(`
      SELECT DISTINCT transaction_file_name
      FROM transaction_e_pos
      WHERE transaction_trade_status IN ('success', 'refund')
      LIMIT 3
    `);

    if (fileNames.rows.length === 0) {
      console.log('❌ No transaction files found to test with');
      return;
    }

    const testFiles = fileNames.rows.map(row => row.transaction_file_name);
    console.log('📁 Testing with files:', testFiles);

    // Test the exact query used in the transaction summary report service
    const summaryQuery = `
      SELECT t.*, m.merchant_id, m.merchant_name, m.withholding_tax, m.transfer_fee,
             b.bank_code, b.bank_name_th, b.bank_name_en, mw.wechat_rate, 7.0 as vat_tax_percent
      FROM transaction_e_pos t
      LEFT JOIN merchant m ON t.transaction_merchant_vat = m.merchant_vat
      LEFT JOIN merchant_bank mb ON m.merchant_id = mb.merchant_id AND mb.active = true
      LEFT JOIN tmst_bank b ON mb.bank_id = b.bank_id AND b.active = true
      LEFT JOIN merchant_wechat mw ON m.merchant_id = mw.merchant_id
      WHERE t.transaction_file_name = ANY($1)
      AND t.transaction_trade_status IN ('success', 'refund')
      ORDER BY t.transaction_merchant_vat, t.transaction_time
      LIMIT 10
    `;

    const result = await client.query(summaryQuery, [testFiles]);
    console.log(`📊 Summary query returned ${result.rows.length} rows:`);

    result.rows.forEach((row, index) => {
      console.log(`\n${index + 1}. ${row.transaction_merchant_name} (VAT: ${row.transaction_merchant_vat})`);
      console.log(`   Amount: ${row.transaction_amount} ${row.transaction_trade_status.toUpperCase()}`);
      console.log(`   Bank Code: ${row.bank_code || 'NULL'}`);
      console.log(`   Bank Name: ${row.bank_name_en || 'NULL'}`);
      console.log(`   Merchant ID: ${row.merchant_id || 'NULL'}`);
      console.log(`   WeChat Rate: ${row.wechat_rate || 'NULL'}%`);
    });

    // Check for any rows with NULL bank codes
    const nullBankRows = result.rows.filter(row => !row.bank_code);
    if (nullBankRows.length > 0) {
      console.log(`\n⚠️  Found ${nullBankRows.length} rows with NULL bank codes:`);
      nullBankRows.forEach(row => {
        console.log(`   - ${row.transaction_merchant_name} (VAT: ${row.transaction_merchant_vat})`);
      });
    } else {
      console.log('\n✅ All rows have bank codes!');
    }

  } finally {
    client.release();
  }
}

// Run both tests
async function runAllTests() {
  await testBankData();
  await testTransactionSummaryQuery();
}

runAllTests()
  .then(() => {
    console.log('\n✅ All bank tests completed');
    process.exit(0);
  })
  .catch(error => {
    console.error('\n❌ Bank tests failed:', error);
    process.exit(1);
  });
