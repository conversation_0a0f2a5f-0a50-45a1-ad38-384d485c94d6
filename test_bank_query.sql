-- Test query to verify bank joins are working correctly
-- This query shows the complete structure with all necessary joins

SELECT 
  t.transaction_id,
  t.transaction_merchant_id,
  t.transaction_merchant_name,
  t.transaction_merchant_vat,
  t.transaction_amount,
  t.transaction_trade_status,
  t.transaction_channel_type,
  
  -- Merchant information
  m.merchant_id,
  m.merchant_name,
  m.withholding_tax,
  m.transfer_fee,
  
  -- WeChat rate information
  mw.wechat_rate,
  
  -- Network service VAT information
  ns.vat_percentage,
  
  -- Bank information (THIS IS THE KEY PART FOR BANK SUBTOTALS)
  b.bank_code,
  b.bank_name_th,
  b.bank_name_en,
  
  -- Merchant bank account information
  mb.bank_account_no,
  mb.bank_account_name

FROM transaction_e_pos t

-- Join with merchant table to get merchant details
LEFT JOIN merchant m ON t.transaction_merchant_vat = m.merchant_vat

-- Join with merchant_wechat to get MDR rates
LEFT JOIN merchant_wechat mw ON m.merchant_id = mw.merchant_id AND mw.active = true

-- Join with network_service to get VAT percentage
LEFT JOIN network_service ns ON ns.active = true

-- JOIN WITH BANK TABLES FOR BANK SUBTOTALS
-- This is the critical part that was missing!
LEFT JOIN merchant_bank mb ON m.merchant_id = mb.merchant_id AND mb.active = true
LEFT JOIN tmst_bank b ON mb.bank_id = b.bank_id AND b.active = true

WHERE t.transaction_trade_status IN ('success', 'refund')
ORDER BY 
  b.bank_code,  -- Group by bank first
  t.transaction_merchant_vat,  -- Then by merchant
  t.transaction_trade_status,  -- Then by transaction type
  t.transaction_time;

-- Expected result structure:
-- bank_code | merchant_name | trade_status | amount | ...
-- BBL       | ABC Company   | SUCCESS      | 1000   | ...
-- BBL       | ABC Company   | REFUND       | -100   | ...
-- BBL       | XYZ Corp      | SUCCESS      | 2000   | ...
-- SCB       | DEF Ltd       | SUCCESS      | 1500   | ...
-- SCB       | DEF Ltd       | REFUND       | -200   | ...

-- This structure allows for:
-- 1. Individual merchant rows (SUCCESS and REFUND separate)
-- 2. Bank subtotals (grouped by bank_code)
-- 3. Grand totals (all banks combined)

-- Bank Subtotal Example:
-- BANK SUBTOTAL - Bangkok Bank (BBL): 2900 THB (3 transactions)
-- BANK SUBTOTAL - Siam Commercial Bank (SCB): 1300 THB (2 transactions)
-- GRAND TOTAL: 4200 THB (5 transactions)
