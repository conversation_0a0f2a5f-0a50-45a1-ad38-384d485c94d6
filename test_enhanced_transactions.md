# Enhanced Transaction Financial Calculations with Separate Success/Refund Rows

## Overview
The enhanced transaction report system now creates **TWO SEPARATE ROWS** per merchant - one for SUCCESS transactions and one for REFUND transactions. This provides clear visibility into each transaction type with proper financial calculations.

## Key Features

### 1. Separate Row Display
- **SUCCESS Row**: Shows only successful transactions with positive amounts and normal fee calculations
- **REFUND Row**: Shows only refund transactions with negative amounts and reversed fee calculations
- **Visual Distinction**: Each row is clearly labeled with SUCCESS/REFUND status and color-coded

### 2. Enhanced Financial Calculations

#### MDR (Merchant Discount Rate)
- Success MDR = Success Amount × (MDR% ÷ 100)
- Refund MDR = Refund Amount × (MDR% ÷ 100)
- Net MDR = Success MDR - Refund MDR

#### VAT (Value Added Tax)
- Success VAT = (Success Transfer Fee + Success MDR) × (VAT% ÷ 100)
- Refund VAT = (Refund Transfer Fee + Refund MDR) × (VAT% ÷ 100)
- Net VAT = Success VAT - Refund VAT

#### Transfer Fee
- Applied to success transactions, deducted for refund transactions
- Net Transfer Fee = Success Transfer Fee - Refund Transfer Fee

#### Reimbursement Fee
- Success Reimbursement = Success Amount × 0.5%
- Refund Reimbursement = Refund Amount × 0.5%
- Net Reimbursement = Success Reimbursement - Refund Reimbursement

#### Withholding Tax
- Applied to net MDR amount
- Withholding Tax = Net MDR × (Withholding Tax% ÷ 100)

### 3. Report Display Enhancements

#### Enhanced Table Structure
- **TXN**: Count of transactions for this specific row (success or refund)
- **Trx Amt**: Transaction amount with proper sign (positive for success, negative for refund)
- **Type**: Clear SUCCESS/REFUND label for each row
- **Color Coding**: Green for SUCCESS rows, red for REFUND rows

#### Visual Indicators
- Refund amounts displayed in red color
- Negative sign prefix for refund amounts
- Clear separation between success and refund data

### 4. Database Schema Updates

#### Updated Interfaces
```typescript
interface TransactionSummaryData {
  // ... existing fields
  successCount: number;
  refundCount: number;
  successAmount: number;
  refundAmount: number;
  // ... rest of fields
}

interface GrandTotalsData {
  // ... existing fields
  totalSuccessTransactions: number;
  totalRefundTransactions: number;
  totalSuccessAmount: number;
  totalRefundAmount: number;
  // ... rest of fields
}
```

### 5. Implementation Details

#### Service Layer Changes
- `ReportService.generateTransactionSummaryReport()`: Enhanced to separate success/refund
- `TransactionSummaryReportService`: Updated financial calculation logic
- Both renderer and main process services updated for consistency

#### UI Component Changes
- `TransactionSummaryReport.tsx`: Updated table headers and data display
- Enhanced financial calculation methodology documentation
- Color-coded display for refund amounts

### 6. Testing Scenarios

#### Test Case 1: Mixed Success/Refund Transactions
```
Merchant A will show TWO ROWS:

Row 1 (SUCCESS):
- 5 transactions: 5000 THB
- MDR (2.5%): 125 THB
- Type: SUCCESS (green)

Row 2 (REFUND):
- 2 transactions: -1000 THB
- MDR (2.5%): -25 THB
- Type: REFUND (red)
```

#### Test Case 2: Success Only
```
Merchant B:
- 3 Success transactions: 2000 THB each = 6000 THB
- 0 Refund transactions = 0 THB
- Net Amount: 6000 THB
- Net MDR (2.5%): 6000 × 0.025 = 150 THB
```

#### Test Case 3: Refund Only
```
Merchant C:
- 0 Success transactions = 0 THB
- 1 Refund transaction: 1500 THB
- Net Amount: 0 - 1500 = -1500 THB
- Net MDR (2.5%): 0 - (1500 × 0.025) = -37.5 THB
```

### 7. Benefits

1. **Accurate Financial Reporting**: Proper handling of refunds with negative calculations
2. **Clear Transaction Breakdown**: Separate visibility of success vs refund transactions
3. **Compliance**: Meets accounting standards for refund processing
4. **Audit Trail**: Enhanced tracking of transaction types and amounts
5. **Business Intelligence**: Better insights into transaction patterns

### 8. Migration Notes

- Existing reports will automatically use the enhanced calculation logic
- Historical data remains unchanged
- New reports will show the enhanced success/refund breakdown
- Database schema is backward compatible

## Usage

1. Upload transaction files with mixed success/refund transactions
2. Generate transaction summary report
3. View enhanced breakdown with success/refund separation
4. Export PDF reports with detailed financial calculations

The enhanced system provides comprehensive financial reporting with proper refund handling, ensuring accurate business intelligence and compliance with accounting standards.
