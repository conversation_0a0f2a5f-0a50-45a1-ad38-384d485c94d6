const { Client } = require('pg');
require('dotenv').config({ path: '.env.local' });

// Import the database connection function
const { getDbConnection } = require('./out/main/index.js');

async function testGetUsers() {
  console.log('🧪 Testing get-users handler logic...');
  
  // Simulate the handler logic
  let client = null;

  try {
    const options = { page: 1, pageSize: 10, search: '' };
    const { page = 1, pageSize = 10, search = '' } = options;
    const offset = (page - 1) * pageSize;

    console.log('📋 [TEST] Handler called with options:', { page, pageSize, search });
    console.log('📋 [TEST] Calculated offset:', offset);
    
    client = await getDbConnection();
    console.log('📋 [TEST] Database connection established');

    // Build the WHERE clause for search
    let whereClause = '';
    let searchParams = [];
    
    if (search && search.trim()) {
      whereClause = 'WHERE LOWER(u.user_name) LIKE LOWER($1) OR LOWER(u.user_ref) LIKE LOWER($1)';
      searchParams.push(`%${search.trim()}%`);
      console.log('📋 [TEST] Search parameters:', searchParams);
    }

    // Get total count for pagination
    const countQuery = `
      SELECT COUNT(*) as total
      FROM tsys_user u
      LEFT JOIN tmas_user_role r ON u.role_code = r.role_code
      ${whereClause}
    `;

    console.log('📋 [TEST] Count query:', countQuery);
    console.log('📋 [TEST] Count params:', searchParams);
    
    const countResult = await client.query(countQuery, searchParams);
    const totalRecords = parseInt(countResult.rows[0].total);
    const totalPages = Math.ceil(totalRecords / pageSize);

    console.log('📋 [TEST] Count result - total records:', totalRecords, 'total pages:', totalPages);

    // Get paginated results
    const dataQuery = `
      SELECT u.user_id, u.user_ref, u.user_name, u.role_code, r.role_name,
             u.active, u.locked, u.failed_attempts, 
             u.last_login,
             u.create_dt, 
             u.update_dt
      FROM tsys_user u
      LEFT JOIN tmas_user_role r ON u.role_code = r.role_code
      ${whereClause}
      ORDER BY u.user_name
      LIMIT $${searchParams.length + 1} OFFSET $${searchParams.length + 2}
    `;

    const dataParams = [...searchParams, pageSize, offset];
    console.log('📋 [TEST] Data query:', dataQuery);
    console.log('📋 [TEST] Data params:', dataParams);
    
    const result = await client.query(dataQuery, dataParams);

    const pagination = {
      currentPage: page,
      totalPages,
      totalRecords,
      hasNextPage: page < totalPages,
      hasPreviousPage: page > 1
    };

    console.log(`✅ [TEST] Found ${result.rows.length} users (page ${page} of ${totalPages})`);
    console.log('✅ [TEST] Pagination:', pagination);
    console.log('✅ [TEST] Sample user:', result.rows[0]);
    
    const response = {
      success: true,
      users: result.rows,
      pagination,
      message: `Found ${totalRecords} users`
    };

    console.log('✅ [TEST] Final response structure:', {
      success: response.success,
      usersCount: response.users?.length,
      pagination: response.pagination,
      message: response.message
    });

  } catch (error) {
    console.error('❌ [TEST] Error fetching users:', error.message);
    console.error('❌ [TEST] Error stack:', error.stack);
    return {
      success: false,
      message: 'Failed to fetch users',
      error: error.message
    };
  } finally {
    if (client) {
      console.log('📋 [TEST] Closing database connection');
      await client.end();
    }
  }
}

testGetUsers().catch(console.error);
