// Test script to verify merchant search functionality
const { Client } = require('pg');
require('dotenv').config({ path: '.env.local' });

async function testMerchantSearch() {
  const client = new Client({
    host: process.env.DB_HOST,
    port: parseInt(process.env.DB_PORT || '5432'),
    database: process.env.DB_NAME,
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    ssl: process.env.DB_SSL === 'true' ? { rejectUnauthorized: false } : false
  });

  try {
    await client.connect();
    console.log('✅ Connected to database');

    // Test the fixed query with search functionality
    const search = 'test';
    const pageSize = 10;
    const offset = 0;
    
    const whereClause = `WHERE (
      UPPER(m.merchant_name) LIKE UPPER($1) OR
      UPPER(m.merchant_vat) LIKE UPPER($2) OR
      UPPER(m.email) LIKE UPPER($3) OR
      UPPER(m.phone) LIKE UPPER($4)
    )`;
    
    const searchParams = [`%${search}%`, `%${search}%`, `%${search}%`, `%${search}%`];

    // Test count query
    const countQuery = `
      SELECT COUNT(*) as total 
      FROM merchant m
      LEFT JOIN merchant pm ON m.parent_merchant_id = pm.merchant_id
      ${whereClause}
    `;
    
    console.log('🔍 Testing count query...');
    const countResult = await client.query(countQuery, searchParams);
    const totalRecords = parseInt(countResult.rows[0].total);
    console.log(`✅ Count query successful: ${totalRecords} records found`);

    // Test data query
    const dataQuery = `
      SELECT m.merchant_id, m.merchant_name, m.merchant_vat, m.merchant_type, m.parent_merchant_id,
             pm.merchant_name as parent_merchant_name, m.merchant_sub_name,
             m.merchant_mcc, m.merchant_id_wechat, m.phone, m.email, m.address, m.zipcode, m.remark,
             m.invoice_name, m.invoice_tax, m.invoice_address, m.contact_person, m.contact_email,
             m.contact_phone, m.contact_fax, m.group_id, m.zone_id, m.product_id, m.category_id,
             m.rate_min_transfer, m.transfer_fee, m.settlement_fee, m.withholding_tax, m.active,
             m.create_by, m.create_dt, m.update_by, m.update_dt
      FROM merchant m
      LEFT JOIN merchant pm ON m.parent_merchant_id = pm.merchant_id
      ${whereClause}
      ORDER BY m.merchant_id ASC
      LIMIT $5 OFFSET $6
    `;

    console.log('🔍 Testing data query...');
    const dataParams = [...searchParams, pageSize, offset];
    const dataResult = await client.query(dataQuery, dataParams);
    console.log(`✅ Data query successful: ${dataResult.rows.length} records returned`);

    // Test without search (should also work)
    console.log('🔍 Testing without search...');
    const simpleQuery = `
      SELECT m.merchant_id, m.merchant_name, m.merchant_type, m.active,
             pm.merchant_name as parent_merchant_name
      FROM merchant m
      LEFT JOIN merchant pm ON m.parent_merchant_id = pm.merchant_id
      ORDER BY m.merchant_id ASC
      LIMIT 5
    `;
    
    const simpleResult = await client.query(simpleQuery);
    console.log(`✅ Simple query successful: ${simpleResult.rows.length} records returned`);

    if (simpleResult.rows.length > 0) {
      console.log('📋 Sample merchant data:');
      simpleResult.rows.forEach(row => {
        console.log(`  - ID: ${row.merchant_id}, Name: ${row.merchant_name}, Type: ${row.merchant_type}, Parent: ${row.parent_merchant_name || 'N/A'}`);
      });
    }

    console.log('🎉 All tests passed! The merchant search functionality is working correctly.');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    process.exit(1);
  } finally {
    await client.end();
  }
}

testMerchantSearch();
