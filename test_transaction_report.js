// Test script to trigger transaction summary report generation
// This will help us see the debugging output for bank codes

const { Pool } = require('pg');

// Database connection (update with your actual connection details)
const pool = new Pool({
  host: 'ep-dawn-fog-a1jk7z7f-pooler.ap-southeast-1.aws.neon.tech',
  database: 'neondb',
  user: 'neondb_owner',
  password: 'npg_v1aKnJdNXif4',
  port: 5432,
  ssl: { rejectUnauthorized: false }
});

async function testTransactionReport() {
  const client = await pool.connect();
  
  try {
    console.log('🔍 Testing transaction summary report generation...\n');

    // Get some transaction file names to test with
    const fileNames = await client.query(`
      SELECT DISTINCT transaction_file_name 
      FROM transaction_e_pos 
      WHERE transaction_trade_status IN ('success', 'refund')
      LIMIT 1
    `);
    
    if (fileNames.rows.length === 0) {
      console.log('❌ No transaction files found to test with');
      return;
    }
    
    const testFiles = fileNames.rows.map(row => row.transaction_file_name);
    console.log('📁 Testing with files:', testFiles);
    
    // Check table existence first
    const tableCheck = await client.query(`
      SELECT
        EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'merchant') as merchant_exists,
        EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'merchant_wechat') as merchant_wechat_exists,
        EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'network_service') as network_service_exists,
        EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'merchant_bank') as merchant_bank_exists,
        EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'tmst_bank') as tmst_bank_exists
    `);
    
    const tableExists = tableCheck.rows[0];
    console.log('📊 Table existence check:', tableExists);
    
    // Test the exact query used in get-enhanced-transactions-for-report handler
    const query = `
      SELECT
        t.*,
        COALESCE(m.withholding_tax, 0) as withholding_tax,
        COALESCE(m.transfer_fee, 0) as transfer_fee,
        COALESCE(mw.wechat_rate, 0) as wechat_rate,
        COALESCE(ns.vat_percentage, 0) as vat_percentage,
        b.bank_code,
        b.bank_name_th,
        b.bank_name_en
      FROM transaction_e_pos t
      LEFT JOIN merchant m ON (
        CASE
          WHEN t.transaction_merchant_id ~ '^[0-9]+$'
          THEN t.transaction_merchant_id::integer = m.merchant_id
          ELSE FALSE
        END
      )
      LEFT JOIN merchant_wechat mw ON m.merchant_id = mw.merchant_id AND mw.active = true
      LEFT JOIN merchant_bank mb ON m.merchant_id = mb.merchant_id AND mb.active = true
      LEFT JOIN tmst_bank b ON mb.bank_id = b.bank_id AND b.active = true
      LEFT JOIN network_service ns ON ns.active = true
      WHERE t.transaction_file_name = ANY($1)
      AND t.transaction_trade_status IN ('success', 'refund')
      ORDER BY t.transaction_time DESC
      LIMIT 10
    `;

    console.log('\n🔍 Executing query:', query);
    console.log('🔍 Parameters:', [testFiles]);

    const result = await client.query(query, [testFiles]);
    console.log(`\n📊 Query returned ${result.rows.length} rows:`);
    
    // Group by merchant VAT like the service does
    const merchantGroups = new Map();
    for (const transaction of result.rows) {
      const vatNumber = transaction.transaction_merchant_vat || 'UNKNOWN';
      if (!merchantGroups.has(vatNumber)) {
        merchantGroups.set(vatNumber, []);
      }
      merchantGroups.get(vatNumber).push(transaction);
    }
    
    console.log(`\n📊 Grouped into ${merchantGroups.size} merchants:`);
    
    for (const [vatNumber, merchantTransactions] of merchantGroups) {
      const firstTransaction = merchantTransactions[0];
      
      console.log(`\n🏢 Merchant ${vatNumber}:`);
      console.log(`   Name: ${firstTransaction.transaction_merchant_name}`);
      console.log(`   Bank Code: ${firstTransaction.bank_code || 'NULL'}`);
      console.log(`   Bank Name: ${firstTransaction.bank_name_en || 'NULL'}`);
      console.log(`   Merchant ID: ${firstTransaction.merchant_id || 'NULL'}`);
      console.log(`   Transaction Count: ${merchantTransactions.length}`);
      
      // Show all bank-related keys in the object
      const bankKeys = Object.keys(firstTransaction).filter(key => key.includes('bank'));
      console.log(`   Bank-related keys: ${bankKeys.join(', ') || 'none'}`);
      
      // Show the actual bank values
      bankKeys.forEach(key => {
        console.log(`     ${key}: ${firstTransaction[key]}`);
      });
    }
    
  } finally {
    client.release();
  }
}

// Run the test
testTransactionReport()
  .then(() => {
    console.log('\n✅ Transaction report test completed');
    process.exit(0);
  })
  .catch(error => {
    console.error('\n❌ Transaction report test failed:', error);
    process.exit(1);
  });
