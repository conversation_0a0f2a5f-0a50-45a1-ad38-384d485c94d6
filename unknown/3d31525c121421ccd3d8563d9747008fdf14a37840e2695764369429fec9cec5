import React from 'react';
import { useRoleAccess } from '../hooks/useRoleAccess';

interface MasterDataRoleInfoProps {
  section: string;
}

/**
 * Component to display role-based access information for Master Data sections
 */
export function MasterDataRoleInfo({ section }: MasterDataRoleInfoProps) {
  const { userRole, hasWriteAccess, isAdmin, isMaker, isViewer } = useRoleAccess();
  
  // Determine access level text
  const accessLevel = hasWriteAccess ? 'Read/Write' : 'Read Only';
  
  // Determine role-specific permissions text
  let permissionsText = '';
  if (isAdmin || isMaker) {
    permissionsText = 'Can view, search, filter, export, add, update, and delete';
  } else if (isViewer) {
    permissionsText = 'Can view, search, filter, and export only';
  } else {
    permissionsText = 'Limited access';
  }
  
  return (
    <div className="bg-blue-50 border border-blue-200 rounded-md p-3 mb-4">
      <div className="flex items-start">
        <div className="flex-shrink-0">
          <svg className="h-5 w-5 text-blue-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
          </svg>
        </div>
        <div className="ml-3 flex-1">
          <h3 className="text-sm font-medium text-blue-800">
            {section} Access Information
          </h3>
          <div className="mt-2 text-sm text-blue-700">
            <p>
              <span className="font-semibold">Role:</span> {userRole?.toUpperCase() || 'Unknown'}
            </p>
            <p>
              <span className="font-semibold">Access Level:</span> {accessLevel}
            </p>
            <p>
              <span className="font-semibold">Permissions:</span> {permissionsText}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

/**
 * Component to display role-based action buttons for Master Data sections
 */
interface MasterDataActionButtonsProps {
  onAdd?: () => void;
  onExport?: () => void;
  addLabel?: string;
  exportLabel?: string;
  showExport?: boolean;
}

export function MasterDataActionButtons({
  onAdd,
  onExport,
  addLabel = 'Add New',
  exportLabel = 'Export CSV',
  showExport = true
}: MasterDataActionButtonsProps) {
  const { canCreate, canExport } = useRoleAccess();
  
  return (
    <div className="flex gap-3">
      {/* Export Button - Available for all roles with export permission */}
      {showExport && canExport && onExport && (
        <button
          onClick={onExport}
          className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          <svg
            className="-ml-1 mr-2 h-5 w-5 text-gray-500"
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path fillRule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clipRule="evenodd" />
          </svg>
          {exportLabel}
        </button>
      )}
      
      {/* Add Button - Only for users with create permission */}
      {canCreate && onAdd && (
        <button
          onClick={onAdd}
          className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          <svg
            className="-ml-1 mr-2 h-5 w-5"
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path fillRule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clipRule="evenodd" />
          </svg>
          {addLabel}
        </button>
      )}
    </div>
  );
}
